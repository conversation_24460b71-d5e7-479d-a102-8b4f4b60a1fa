"""Configuration management for the product image automation system."""

import os
from typing import Optional
from pydantic import Field, field_validator, ConfigDict
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class Settings(BaseSettings):
    """Application settings with validation."""

    # API Keys
    serper_api_key: Optional[str] = Field(None, env="SERPER_API_KEY", description="Serper.dev API key for image search")
    gemini_api_key: Optional[str] = Field(None, env="GEMINI_API_KEY", description="Google Gemini API key for image assessment")

    # Search Configuration
    max_images_per_search: int = Field(10, env="MAX_IMAGES_PER_SEARCH", ge=1, le=50)
    max_concurrent_requests: int = Field(5, env="MAX_CONCURRENT_REQUESTS", ge=1, le=20)
    image_quality_threshold: int = Field(6, env="IMAGE_QUALITY_THRESHOLD", ge=1, le=10)
    request_timeout: int = Field(30, env="REQUEST_TIMEOUT", ge=5, le=120)

    # Gemini Model Configuration
    gemini_model: str = Field("gemini-2.5-flash-lite-preview-06-17", env="GEMINI_MODEL")
    gemini_temperature: float = Field(0.4, env="GEMINI_TEMPERATURE", ge=0.0, le=2.0)
    gemini_max_tokens: int = Field(1000, env="GEMINI_MAX_TOKENS", ge=100, le=8192)

    # Logging
    log_level: str = Field("INFO", env="LOG_LEVEL")
    log_format: str = Field("json", env="LOG_FORMAT")

    # Output Configuration
    output_dir: str = Field("./output", env="OUTPUT_DIR")
    save_images: bool = Field(False, env="SAVE_IMAGES")
    save_metadata: bool = Field(True, env="SAVE_METADATA")

    @field_validator('log_level')
    @classmethod
    def validate_log_level(cls, v):
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f'Log level must be one of: {valid_levels}')
        return v.upper()

    @field_validator('log_format')
    @classmethod
    def validate_log_format(cls, v):
        valid_formats = ['json', 'text']
        if v.lower() not in valid_formats:
            raise ValueError(f'Log format must be one of: {valid_formats}')
        return v.lower()

    @field_validator('output_dir')
    @classmethod
    def validate_output_dir(cls, v):
        # Create output directory if it doesn't exist
        os.makedirs(v, exist_ok=True)
        return v

    model_config = ConfigDict(
        env_file=".env",
        case_sensitive=False
    )


# Global settings instance
settings = Settings()