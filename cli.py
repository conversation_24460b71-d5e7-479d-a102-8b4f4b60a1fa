#!/usr/bin/env python3
"""Command-line interface for the Product Image Automation system."""

import argparse
import asyncio
import sys
from pathlib import Path
from typing import List, Optional

from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from rich.table import Table
from rich.panel import Panel
from rich import print as rprint

from config import settings
from product_image_automation import ProductImageAutomation
from utils import (
    save_results_to_json,
    save_results_to_csv,
    create_html_report,
    generate_summary_report,
    load_product_list
)

console = Console()


def print_banner():
    """Print application banner."""
    banner = """
╔═══════════════════════════════════════════════════════════════╗
║                Product Image Automation                       ║
║                                                               ║
║  Automated product image search and processing for grocery    ║
║  and retail businesses using AI-powered image assessment      ║
╚═══════════════════════════════════════════════════════════════╝
    """
    console.print(banner, style="bold blue")


def validate_api_keys() -> bool:
    """Validate that required API keys are configured.

    Returns:
        True if all required API keys are present, False otherwise
    """
    missing_keys = []

    if not settings.serper_api_key:
        missing_keys.append("SERPER_API_KEY")

    if not settings.gemini_api_key:
        missing_keys.append("GEMINI_API_KEY")

    if missing_keys:
        console.print("\n[bold red]Error: Missing required API keys![/bold red]")
        console.print("\nPlease set the following environment variables:")
        for key in missing_keys:
            console.print(f"  • {key}")
        console.print("\nYou can also create a .env file with these variables.")
        console.print("See .env.example for reference.")
        return False

    return True


async def test_system() -> bool:
    """Test system connectivity and configuration.

    Returns:
        True if all tests pass, False otherwise
    """
    console.print("\n[bold yellow]Testing system connectivity...[/bold yellow]")

    async with ProductImageAutomation() as automation:
        test_results = await automation.test_system()

    # Create test results table
    table = Table(title="System Test Results")
    table.add_column("Component", style="cyan")
    table.add_column("Status", justify="center")
    table.add_column("Details")

    all_passed = True

    for component, passed in test_results.items():
        status = "[green]✓ PASS[/green]" if passed else "[red]✗ FAIL[/red]"
        details = "Connected successfully" if passed else "Connection failed"

        if component == "serper":
            component_name = "Serper.dev API"
        elif component == "gemini":
            component_name = "Google Gemini API"
        elif component == "output_directory":
            component_name = "Output Directory"
        else:
            component_name = component.title()

        table.add_row(component_name, status, details)

        if not passed:
            all_passed = False

    console.print(table)

    if all_passed:
        console.print("\n[bold green]✓ All system tests passed![/bold green]")
    else:
        console.print("\n[bold red]✗ Some system tests failed. Please check your configuration.[/bold red]")

    return all_passed


async def process_products(
    products: List[str],
    search_front: bool = True,
    search_back: bool = True,
    download_images: bool = True,
    max_concurrent: int = 3,
    output_formats: List[str] = None
) -> bool:
    """Process a list of products.

    Args:
        products: List of product names to process
        search_front: Whether to search for front view images
        search_back: Whether to search for back view images
        download_images: Whether to download images locally
        max_concurrent: Maximum concurrent processing
        output_formats: List of output formats (json, csv, html)

    Returns:
        True if processing completed successfully, False otherwise
    """
    if not products:
        console.print("[red]No products to process![/red]")
        return False

    output_formats = output_formats or ['json']

    console.print(f"\n[bold green]Processing {len(products)} products...[/bold green]")
    console.print(f"Search front view: {search_front}")
    console.print(f"Search back view: {search_back}")
    console.print(f"Download images: {download_images}")
    console.print(f"Max concurrent: {max_concurrent}")

    # Create progress bar
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TaskProgressColumn(),
        console=console
    ) as progress:

        # Add main task
        main_task = progress.add_task(
            "Processing products...",
            total=len(products)
        )

        async with ProductImageAutomation() as automation:
            # Process products in batches to show progress
            batch_size = min(max_concurrent, 10)
            results_list = []

            for i in range(0, len(products), batch_size):
                batch = products[i:i + batch_size]

                # Update progress description
                progress.update(
                    main_task,
                    description=f"Processing batch {i//batch_size + 1}/{(len(products) + batch_size - 1)//batch_size}..."
                )

                # Process batch
                batch_results = await automation.process_product_batch(
                    product_names=batch,
                    search_front=search_front,
                    search_back=search_back,
                    download_images=download_images,
                    max_concurrent=max_concurrent
                )

                results_list.extend(batch_results.products)

                # Update progress
                progress.update(main_task, advance=len(batch))

            # Combine all results
            from models import BatchProcessingResult
            final_results = BatchProcessingResult(
                products=results_list,
                total_products=len(products),
                successful_products=sum(1 for r in results_list if r.success),
                failed_products=sum(1 for r in results_list if not r.success),
                total_processing_time=sum(r.processing_time for r in results_list),
                average_processing_time=sum(r.processing_time for r in results_list) / len(results_list) if results_list else 0.0
            )

    # Generate summary
    summary = generate_summary_report(final_results)

    # Display results summary
    console.print("\n[bold green]Processing Complete![/bold green]")

    # Create summary table
    summary_table = Table(title="Processing Summary")
    summary_table.add_column("Metric", style="cyan")
    summary_table.add_column("Value", justify="right")

    summary_table.add_row("Total Products", str(summary['total_products']))
    summary_table.add_row("Successful", f"[green]{summary['processing_summary']['successful_products']}[/green]")
    summary_table.add_row("Failed", f"[red]{summary['processing_summary']['failed_products']}[/red]")
    summary_table.add_row("Success Rate", f"{summary['processing_summary']['success_rate']:.1%}")
    summary_table.add_row("Total Images Found", str(summary['image_summary']['total_images_found']))
    summary_table.add_row("Front View Images", str(summary['image_summary']['front_view_images']))
    summary_table.add_row("Back View Images", str(summary['image_summary']['back_view_images']))
    summary_table.add_row("With Nutrition Info", str(summary['quality_summary']['images_with_nutrition_info']))
    summary_table.add_row("Avg Quality Score", f"{summary['quality_summary']['average_quality_score']:.1f}/10")
    summary_table.add_row("Total Processing Time", f"{summary['processing_summary']['total_processing_time']:.1f}s")

    console.print(summary_table)

    # Save results in requested formats
    output_dir = Path(settings.output_dir)
    timestamp = asyncio.get_event_loop().time()

    console.print("\n[bold yellow]Saving results...[/bold yellow]")

    saved_files = []

    if 'json' in output_formats:
        json_path = output_dir / "results" / f"results_{int(timestamp)}.json"
        save_results_to_json(final_results, str(json_path))
        saved_files.append(str(json_path))
        console.print(f"[green]✓[/green] JSON report saved: {json_path}")

    if 'csv' in output_formats:
        csv_path = output_dir / "results" / f"results_{int(timestamp)}.csv"
        save_results_to_csv(final_results, str(csv_path))
        saved_files.append(str(csv_path))
        console.print(f"[green]✓[/green] CSV report saved: {csv_path}")

    if 'html' in output_formats:
        html_path = output_dir / "results" / f"report_{int(timestamp)}.html"
        create_html_report(final_results, str(html_path))
        saved_files.append(str(html_path))
        console.print(f"[green]✓[/green] HTML report saved: {html_path}")

    # Show failed products if any
    failed_products = [r for r in final_results.products if not r.success]
    if failed_products:
        console.print("\n[bold red]Failed Products:[/bold red]")
        for result in failed_products[:10]:  # Show first 10 failures
            console.print(f"  • {result.product_name}: {result.error_message}")

        if len(failed_products) > 10:
            console.print(f"  ... and {len(failed_products) - 10} more")

    return True


def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(
        description="Product Image Automation - AI-powered product image search and processing",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Test system connectivity
  python cli.py test

  # Process a single product
  python cli.py process "Coca Cola 12oz Can"

  # Process multiple products
  python cli.py process "Coca Cola" "Pepsi" "Sprite"

  # Process products from a file
  python cli.py process --file products.txt

  # Search only back views with nutrition info
  python cli.py process --no-front --back "Cheerios Cereal"

  # Generate all output formats
  python cli.py process --output json,csv,html "Apple iPhone 13"
        """
    )

    subparsers = parser.add_subparsers(dest='command', help='Available commands')

    # Test command
    test_parser = subparsers.add_parser('test', help='Test system connectivity')

    # Process command
    process_parser = subparsers.add_parser('process', help='Process products')
    process_parser.add_argument(
        'products',
        nargs='*',
        help='Product names to process'
    )
    process_parser.add_argument(
        '--file', '-f',
        type=str,
        help='File containing product names (txt, csv, or json)'
    )
    process_parser.add_argument(
        '--front',
        action='store_true',
        default=True,
        help='Search for front view images (default: True)'
    )
    process_parser.add_argument(
        '--no-front',
        action='store_true',
        help='Skip front view image search'
    )
    process_parser.add_argument(
        '--back',
        action='store_true',
        default=True,
        help='Search for back view images (default: True)'
    )
    process_parser.add_argument(
        '--no-back',
        action='store_true',
        help='Skip back view image search'
    )
    process_parser.add_argument(
        '--no-download',
        action='store_true',
        help='Skip downloading images (only assess URLs)'
    )
    process_parser.add_argument(
        '--concurrent', '-c',
        type=int,
        default=3,
        help='Maximum concurrent processing (default: 3)'
    )
    process_parser.add_argument(
        '--output', '-o',
        type=str,
        default='json',
        help='Output formats: json,csv,html (default: json)'
    )

    args = parser.parse_args()

    # Print banner
    print_banner()

    # Validate API keys
    if not validate_api_keys():
        sys.exit(1)

    if args.command == 'test':
        # Test system
        success = asyncio.run(test_system())
        sys.exit(0 if success else 1)

    elif args.command == 'process':
        # Get products list
        products = []

        if args.file:
            try:
                products = load_product_list(args.file)
                console.print(f"[green]Loaded {len(products)} products from {args.file}[/green]")
            except Exception as e:
                console.print(f"[red]Error loading products from file: {e}[/red]")
                sys.exit(1)

        if args.products:
            products.extend(args.products)

        if not products:
            console.print("[red]No products specified. Use product names as arguments or --file option.[/red]")
            sys.exit(1)

        # Parse search options
        search_front = args.front and not args.no_front
        search_back = args.back and not args.no_back
        download_images = not args.no_download

        if not search_front and not search_back:
            console.print("[red]At least one of --front or --back must be enabled.[/red]")
            sys.exit(1)

        # Parse output formats
        output_formats = [fmt.strip().lower() for fmt in args.output.split(',')]
        valid_formats = {'json', 'csv', 'html'}
        invalid_formats = set(output_formats) - valid_formats

        if invalid_formats:
            console.print(f"[red]Invalid output formats: {', '.join(invalid_formats)}[/red]")
            console.print(f"Valid formats: {', '.join(valid_formats)}")
            sys.exit(1)

        # Process products
        try:
            success = asyncio.run(process_products(
                products=products,
                search_front=search_front,
                search_back=search_back,
                download_images=download_images,
                max_concurrent=args.concurrent,
                output_formats=output_formats
            ))
            sys.exit(0 if success else 1)
        except KeyboardInterrupt:
            console.print("\n[yellow]Processing interrupted by user.[/yellow]")
            sys.exit(1)
        except Exception as e:
            console.print(f"\n[red]Error during processing: {e}[/red]")
            sys.exit(1)

    else:
        # No command specified
        parser.print_help()
        sys.exit(1)


if __name__ == '__main__':
    main()