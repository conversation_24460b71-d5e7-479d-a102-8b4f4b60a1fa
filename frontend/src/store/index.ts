import { create } from 'zustand';

interface Product {
  id: string;
  name: string;
  images: Array<{
    url: string;
    score: number;
  }>;
}

interface JobState {
  status: 'idle' | 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  message?: string;
}

interface AppState {
  currentJobId: string | null;
  jobState: JobState;
  products: Product[];
  selectedProductId: string | null;
  setCurrentJobId: (jobId: string | null) => void;
  updateJobState: (state: Partial<JobState>) => void;
  setProducts: (products: Product[]) => void;
  setSelectedProductId: (productId: string | null) => void;
}

export const useAppStore = create<AppState>((set) => ({
  currentJobId: null,
  jobState: {
    status: 'idle',
    progress: 0,
  },
  products: [],
  selectedProductId: null,

  setCurrentJobId: (jobId) => set({ currentJobId: jobId }),

  updateJobState: (state) =>
    set((prev) => ({
      jobState: { ...prev.jobState, ...state },
    })),

  setProducts: (products) => set({ products }),

  setSelectedProductId: (productId) => set({ selectedProductId: productId }),
}));