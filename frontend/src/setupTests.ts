// Setup file for Jest tests
import '@testing-library/jest-dom';

// Mock IntersectionObserver
class MockIntersectionObserver {
  root: Element | null = null;
  rootMargin: string = '0px';
  thresholds: ReadonlyArray<number> = [0];

  constructor(callback?: IntersectionObserverCallback, options?: IntersectionObserverInit) {}

  observe(target: Element): void {}
  unobserve(target: Element): void {}
  disconnect(): void {}
  takeRecords(): IntersectionObserverEntry[] {
    return [];
  }
}

global.IntersectionObserver = MockIntersectionObserver as any;

// Mock ResizeObserver
class MockResizeObserver {
  constructor(callback?: ResizeObserverCallback) {}
  observe(target: Element, options?: ResizeObserverOptions): void {}
  unobserve(target: Element): void {}
  disconnect(): void {}
}

global.ResizeObserver = MockResizeObserver as any;

// Mock window.performance.memory
Object.defineProperty(window, 'performance', {
  value: {
    ...window.performance,
    memory: {
      usedJSHeapSize: 1000000,
      totalJSHeapSize: 2000000,
      jsHeapSizeLimit: 4000000,
    },
  },
  writable: true,
});

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock window.scrollTo
Object.defineProperty(window, 'scrollTo', {
  value: jest.fn(),
  writable: true,
});

// Mock requestAnimationFrame and cancelAnimationFrame
global.requestAnimationFrame = jest.fn((cb) => {
  setTimeout(cb, 0);
  return 1;
});

global.cancelAnimationFrame = jest.fn();

// Mock URL.createObjectURL and URL.revokeObjectURL
global.URL.createObjectURL = jest.fn(() => 'mocked-url');
global.URL.revokeObjectURL = jest.fn();

// Custom Jest matcher for timing assertions
expect.extend({
  toHaveBeenCalledWithinTime(received: any, timeMs: number) {
    const calls = received.mock.calls;
    if (calls.length === 0) {
      return {
        message: () => 'Expected function to have been called',
        pass: false,
      };
    }

    const lastCallTime = received.mock.invocationCallOrder[calls.length - 1];
    const timeDiff = Date.now() - lastCallTime;

    const pass = timeDiff <= timeMs;
    return {
      message: () =>
        pass
          ? `Expected function not to have been called within ${timeMs}ms`
          : `Expected function to have been called within ${timeMs}ms, but it took ${timeDiff}ms`,
      pass,
    };
  },
});

// Extend Jest timeout for performance tests
jest.setTimeout(30000);

// Declare global types for Jest matchers
declare global {
  namespace jest {
    interface Matchers<R> {
      toHaveBeenCalledWithinTime(timeMs: number): R;
    }
  }
}