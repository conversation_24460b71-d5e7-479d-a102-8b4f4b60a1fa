import { ImageReview } from '../components/ImageReview/ImageReview';
import { useAppStore } from '../store';

export function ReviewPage() {
  const selectedProductId = useAppStore((state) => state.selectedProductId);

  if (!selectedProductId) {
    return (
      <div className="text-center py-12">
        <h2 className="text-xl font-semibold mb-4">No Product Selected</h2>
        <p className="text-muted-foreground">
          Please select a product from the Products page to start reviewing images.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-semibold">Image Review</h2>
      <ImageReview />
    </div>
  );
}