import { ProductList } from '../components/ProductList/ProductList';
import { useAppStore } from '../store';
import { ProcessingDashboard } from '../components/ProcessingDashboard/ProcessingDashboard';
import { ImageReview } from '../components/ImageReview/ImageReview';
import { ProductGrid } from '../components/ProductList/ProductGrid';
import { ResultsExport } from '../components/ResultsExport/ResultsExport';

export function HomePage() {
  const jobState = useAppStore((state) => state.jobState);
  const selectedProductId = useAppStore((state) => state.selectedProductId);

  return (
    <div className="space-y-6">
      {/* Show product list upload when no job is running */}
      {jobState.status === 'idle' && <ProductList />}

      {/* Show processing dashboard when job is running or complete */}
      {jobState.status !== 'idle' && <ProcessingDashboard />}

      {/* Show results section when job is complete */}
      {jobState.status === 'completed' && (
        <div className="mt-8 space-y-8">
          {selectedProductId ? (
            <ImageReview />
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-6">
                <h2 className="text-xl font-semibold">Products</h2>
                <ProductGrid />
              </div>
              <ResultsExport />
            </div>
          )}
        </div>
      )}
    </div>
  );
}