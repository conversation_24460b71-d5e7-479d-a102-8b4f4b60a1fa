import React from 'react';
import AnalyticsDashboard from '../components/Analytics/AnalyticsDashboard';
import AdvancedExportOptions from '../components/Export/AdvancedExportOptions';
import { useAnalytics } from '../hooks/useAnalytics';
import { useExport } from '../hooks/useExport';
import { Loader2 } from 'lucide-react';

const AnalyticsPage: React.FC = () => {
  const { processingStats, qualityMetrics, isLoading, error } = useAnalytics();
  const { exportData, isExporting, exportProgress } = useExport();

  const availableCategories = qualityMetrics?.categoryScores.map(c => c.category) || [];

  const handleExport = async (format: string, filters: any) => {
    try {
      // In a real app, you would fetch the actual image data based on filters
      // For now, we'll use mock data
      const mockImageData = [
        {
          id: '1',
          url: 'https://example.com/image1.jpg',
          score: 0.85,
          status: 'approved' as const,
          category: 'Product',
          processedAt: '2024-01-01T10:00:00Z',
          processingTime: 120,
          metadata: { width: 800, height: 600 }
        },
        {
          id: '2',
          url: 'https://example.com/image2.jpg',
          score: 0.65,
          status: 'rejected' as const,
          category: 'Lifestyle',
          processedAt: '2024-01-02T11:00:00Z',
          processingTime: 150,
          metadata: { width: 1200, height: 800 }
        }
      ];

      await exportData({
        format: format as any,
        data: mockImageData,
        filters,
        processingStats,
        qualityMetrics
      });
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
        <span className="ml-2 text-gray-600">Loading analytics...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-red-800 mb-2">Error Loading Analytics</h3>
        <p className="text-red-600">Failed to load analytics data. Please try again later.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">Analytics</h1>
      </div>

      {/* Analytics Dashboard */}
      {processingStats && qualityMetrics && (
        <AnalyticsDashboard
          processingStats={processingStats}
          qualityMetrics={qualityMetrics}
        />
      )}

      {/* Export Options */}
      <AdvancedExportOptions
        onExport={handleExport}
        isExporting={isExporting}
        availableCategories={availableCategories}
      />

      {/* Export Progress */}
      {isExporting && exportProgress > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-blue-800">Exporting...</span>
            <span className="text-sm text-blue-600">{exportProgress}%</span>
          </div>
          <div className="w-full bg-blue-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${exportProgress}%` }}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default AnalyticsPage;