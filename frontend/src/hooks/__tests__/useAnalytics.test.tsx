import React from 'react';
import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useAnalytics } from '../useAnalytics';

// Mock data
const mockImageData = [
  {
    id: '1',
    score: 0.85,
    processedAt: '2024-01-01T10:00:00Z',
    status: 'approved',
    processingTime: 120,
    category: 'Product'
  },
  {
    id: '2',
    score: 0.65,
    processedAt: '2024-01-01T11:00:00Z',
    status: 'approved',
    processingTime: 150,
    category: 'Lifestyle'
  },
  {
    id: '3',
    score: 0.45,
    processedAt: '2024-01-02T10:00:00Z',
    status: 'rejected',
    processingTime: 90,
    category: 'Product'
  }
] as const;

// Mock fetch function
global.fetch = jest.fn();

describe('useAnalytics', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    jest.useFakeTimers();
    queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false,
        },
      },
    });
    (global.fetch as jest.Mock).mockClear();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );

  it('fetches and calculates analytics data correctly', async () => {
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => mockImageData,
    });

    const { result } = renderHook(() => useAnalytics(), { wrapper });

    // Initial state
    expect(result.current.isLoading).toBe(true);
    expect(result.current.error).toBeNull();

    // Wait for data to be loaded
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Check processing stats
    expect(result.current.processingStats).toEqual({
      totalProcessed: 3,
      successRate: (2 / 3) * 100, // 2 approved out of 3
      averageProcessingTime: 120, // (120 + 150 + 90) / 3
      totalImages: 3,
      approved: 2,
      rejected: 1
    });

    // Check quality metrics
    expect(result.current.qualityMetrics.averageScore).toBeCloseTo(0.65); // (0.85 + 0.65 + 0.45) / 3

    // Check score distribution
    const distribution = result.current.qualityMetrics.scoreDistribution;
    expect(distribution).toHaveLength(5); // 5 ranges (0-20%, 20-40%, etc.)
    expect(distribution.find(d => d.range === '40-60%')?.count).toBe(1); // One score in 40-60% range
    expect(distribution.find(d => d.range === '60-80%')?.count).toBe(1); // One score in 60-80% range
    expect(distribution.find(d => d.range === '80-100%')?.count).toBe(1); // One score in 80-100% range

    // Check quality trend
    const trend = result.current.qualityMetrics.qualityTrend;
    expect(trend).toHaveLength(2); // Two unique dates
    expect(trend[0].date).toBe('1/1/2024');
    expect(trend[0].score).toBeCloseTo(0.75); // Average of day 1 scores
    expect(trend[1].date).toBe('1/2/2024');
    expect(trend[1].score).toBe(0.45); // Single score on day 2

    // Check category scores
    const categoryScores = result.current.qualityMetrics.categoryScores;
    expect(categoryScores).toHaveLength(2); // Two categories
    const productScore = categoryScores.find(c => c.category === 'Product');
    expect(productScore?.score).toBeCloseTo(0.65); // Average of Product scores
    const lifestyleScore = categoryScores.find(c => c.category === 'Lifestyle');
    expect(lifestyleScore?.score).toBe(0.65); // Single Lifestyle score
  });

  it('handles API errors gracefully', async () => {
    (global.fetch as jest.Mock).mockRejectedValueOnce(
      new Error('Failed to fetch')
    );

    const { result } = renderHook(() => useAnalytics(), { wrapper });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.error).toBeTruthy();
    expect(result.current.processingStats).toEqual({
      totalProcessed: 0,
      successRate: 0,
      averageProcessingTime: 0,
      totalImages: 0,
      approved: 0,
      rejected: 0
    });
  });

  it('handles empty data correctly', async () => {
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => [],
    });

    const { result } = renderHook(() => useAnalytics(), { wrapper });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.processingStats).toEqual({
      totalProcessed: 0,
      successRate: 0,
      averageProcessingTime: 0,
      totalImages: 0,
      approved: 0,
      rejected: 0
    });

    expect(result.current.qualityMetrics.scoreDistribution).toHaveLength(5);
    expect(result.current.qualityMetrics.qualityTrend).toHaveLength(0);
    expect(result.current.qualityMetrics.categoryScores).toHaveLength(0);
  });

  it('caches data and refetches at correct intervals', async () => {
    const fetchMock = global.fetch as jest.Mock;
    fetchMock
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockImageData,
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => [
          ...mockImageData,
          {
            id: '4',
            score: 0.95,
            processedAt: '2024-01-02T12:00:00Z',
            status: 'approved',
            processingTime: 100,
            category: 'Product'
          }
        ],
      });

    const { result } = renderHook(() => useAnalytics(), { wrapper });

    // Wait for initial data
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(fetchMock).toHaveBeenCalledTimes(1);
    expect(result.current.processingStats.totalImages).toBe(3);

    // Fast-forward time by 6 minutes to trigger refetch
    jest.advanceTimersByTime(6 * 60 * 1000);

    // Wait for refetch
    await waitFor(() => {
      expect(fetchMock).toHaveBeenCalledTimes(2);
    });

    expect(result.current.processingStats.totalImages).toBe(4);
  });
});