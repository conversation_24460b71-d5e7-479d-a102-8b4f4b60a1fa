import React from 'react';
import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useExport } from '../useExport';
import { saveAs } from 'file-saver';
import JSZ<PERSON> from 'jszip';
import jsPDF from 'jspdf';

// Mock dependencies
jest.mock('file-saver');
jest.mock('jszip');
jest.mock('jspdf');
jest.mock('jspdf-autotable');

const mockSaveAs = saveAs as jest.MockedFunction<typeof saveAs>;
const mockJSZip = JSZip as jest.MockedClass<typeof JSZip>;
const mockJsPDF = jsPDF as jest.MockedClass<typeof jsPDF>;

// Mock fetch
global.fetch = jest.fn();

describe('useExport', () => {
  let queryClient: QueryClient;

  const mockImageData = [
    {
      id: '1',
      url: 'https://example.com/image1.jpg',
      score: 0.85,
      status: 'approved' as const,
      category: 'Product',
      processedAt: '2024-01-01T10:00:00Z',
      processingTime: 120,
      metadata: { width: 800, height: 600 }
    },
    {
      id: '2',
      url: 'https://example.com/image2.jpg',
      score: 0.45,
      status: 'rejected' as const,
      category: 'Lifestyle',
      processedAt: '2024-01-02T11:00:00Z',
      processingTime: 150,
      metadata: { width: 1200, height: 800 }
    }
  ];

  const mockFilters = {
    dateRange: { start: '', end: '' },
    scoreRange: { min: 0, max: 100 },
    status: ['approved', 'rejected', 'pending'],
    categories: ['Product', 'Lifestyle']
  };

  const mockProcessingStats = {
    totalProcessed: 100,
    successRate: 85,
    averageProcessingTime: 135,
    totalImages: 100,
    approved: 85,
    rejected: 15
  };

  const mockQualityMetrics = {
    averageScore: 0.65,
    scoreDistribution: [
      { range: '0-20%', count: 5 },
      { range: '20-40%', count: 10 },
      { range: '40-60%', count: 25 },
      { range: '60-80%', count: 35 },
      { range: '80-100%', count: 25 }
    ],
    qualityTrend: [
      { date: '2024-01-01', score: 0.65 },
      { date: '2024-01-02', score: 0.45 }
    ],
    categoryScores: [
      { category: 'Product', score: 0.85 },
      { category: 'Lifestyle', score: 0.45 }
    ]
  };

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false }
      }
    });

    jest.clearAllMocks();

    // Add Blob.text() polyfill for testing
    if (!Blob.prototype.text) {
      Blob.prototype.text = function() {
        return new Promise((resolve) => {
          const reader = new FileReader();
          reader.onload = () => resolve(reader.result as string);
          reader.readAsText(this);
        });
      };
    }

    // Setup saveAs mock to capture the actual Blob
    mockSaveAs.mockImplementation((data: string | Blob, filename?: string) => {
      // Store the blob for testing - ensure it's a real Blob
      (mockSaveAs as any).lastBlob = data;
      (mockSaveAs as any).lastFilename = filename;
    });

    // Setup JSZip mock
    const mockZipInstance = {
      file: jest.fn(),
      generateAsync: jest.fn().mockResolvedValue(new Blob(['zip content']))
    };
    mockJSZip.mockImplementation(() => mockZipInstance as any);

    // Setup jsPDF mock
    const mockPdfInstance = {
      setFontSize: jest.fn(),
      text: jest.fn(),
      addPage: jest.fn(),
      save: jest.fn(),
      autoTable: jest.fn()
    };
    mockJsPDF.mockImplementation(() => mockPdfInstance as any);

    // Setup fetch mock
    (global.fetch as jest.Mock).mockResolvedValue({
      blob: () => Promise.resolve(new Blob(['image data']))
    });
  });

  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );

  describe('CSV Export', () => {
    it('exports data to CSV format', async () => {
      const { result } = renderHook(() => useExport(), { wrapper });

      await result.current.exportData({
        format: 'csv',
        data: mockImageData,
        filters: mockFilters
      });

      await waitFor(() => {
        expect(mockSaveAs).toHaveBeenCalledWith(
          expect.any(Blob),
          expect.stringMatching(/image-analysis-export-.*\.csv$/)
        );
      });

      const savedBlob = (mockSaveAs as any).lastBlob as Blob;
      expect(savedBlob.type).toBe('text/csv;charset=utf-8;');
    });

    it('filters data correctly for CSV export', async () => {
      const { result } = renderHook(() => useExport(), { wrapper });

      const restrictiveFilters = {
        ...mockFilters,
        scoreRange: { min: 80, max: 100 }, // Only high scores
        status: ['approved'] // Only approved
      };

      await result.current.exportData({
        format: 'csv',
        data: mockImageData,
        filters: restrictiveFilters
      });

      await waitFor(() => {
        expect(mockSaveAs).toHaveBeenCalled();
      });

      // Should only include the first item (score 85%, approved)
      const savedBlob = (mockSaveAs as any).lastBlob as Blob;
      const csvContent = await savedBlob.text();
      expect(csvContent).toContain('85.00'); // First item score
      expect(csvContent).not.toContain('45.00'); // Second item score (filtered out)
    });
  });

  describe('JSON Export', () => {
    it('exports data to JSON format', async () => {
      const { result } = renderHook(() => useExport(), { wrapper });

      await result.current.exportData({
        format: 'json',
        data: mockImageData,
        filters: mockFilters
      });

      await waitFor(() => {
        expect(mockSaveAs).toHaveBeenCalledWith(
          expect.any(Blob),
          expect.stringMatching(/image-analysis-export-.*\.json$/)
        );
      });

      const savedBlob = (mockSaveAs as any).lastBlob as Blob;
      expect(savedBlob.type).toBe('application/json;charset=utf-8;');
    });

    it('includes metadata in JSON export', async () => {
      const { result } = renderHook(() => useExport(), { wrapper });

      await result.current.exportData({
        format: 'json',
        data: mockImageData,
        filters: mockFilters
      });

      await waitFor(() => {
        expect(mockSaveAs).toHaveBeenCalled();
      });

      const savedBlob = mockSaveAs.mock.calls[0][0] as Blob;
      const jsonContent = await savedBlob.text();
      const parsedData = JSON.parse(jsonContent);

      expect(parsedData).toHaveProperty('exportedAt');
      expect(parsedData).toHaveProperty('filters');
      expect(parsedData).toHaveProperty('totalRecords', 2);
      expect(parsedData).toHaveProperty('data');
      expect(parsedData.data).toHaveLength(2);
    });
  });

  describe('PDF Export', () => {
    it('exports data to PDF format', async () => {
      const { result } = renderHook(() => useExport(), { wrapper });

      await result.current.exportData({
        format: 'pdf',
        data: mockImageData,
        filters: mockFilters,
        processingStats: mockProcessingStats,
        qualityMetrics: mockQualityMetrics
      });

      await waitFor(() => {
        expect(mockJsPDF).toHaveBeenCalled();
      });

      const pdfInstance = mockJsPDF.mock.results[0].value;
      expect(pdfInstance.setFontSize).toHaveBeenCalled();
      expect(pdfInstance.text).toHaveBeenCalledWith('Image Analysis Report', 20, 20);
      expect(pdfInstance.save).toHaveBeenCalledWith(
        expect.stringMatching(/image-analysis-report-.*\.pdf$/)
      );
    });

    it('throws error when PDF export lacks required data', async () => {
      const { result } = renderHook(() => useExport(), { wrapper });

      await expect(
        result.current.exportData({
          format: 'pdf',
          data: mockImageData,
          filters: mockFilters
          // Missing processingStats and qualityMetrics
        })
      ).rejects.toThrow('Processing stats and quality metrics required for PDF export');
    });
  });

  describe('Images Export', () => {
    it('exports images as ZIP archive', async () => {
      const { result } = renderHook(() => useExport(), { wrapper });

      await result.current.exportData({
        format: 'images',
        data: mockImageData,
        filters: mockFilters
      });

      await waitFor(() => {
        expect(mockJSZip).toHaveBeenCalled();
      });

      const zipInstance = mockJSZip.mock.results[0].value;
      expect(zipInstance.file).toHaveBeenCalledWith(
        'Product/approved/1.jpg',
        expect.any(Blob)
      );
      expect(zipInstance.file).toHaveBeenCalledWith(
        'Lifestyle/rejected/2.jpg',
        expect.any(Blob)
      );
      expect(zipInstance.file).toHaveBeenCalledWith(
        'metadata.json',
        expect.any(String)
      );

      expect(mockSaveAs).toHaveBeenCalledWith(
        expect.any(Blob),
        expect.stringMatching(/image-analysis-images-.*\.zip$/)
      );
    });

    it('updates export progress during image download', async () => {
      // Mock fetch with delay to simulate real network behavior
      (global.fetch as jest.Mock).mockImplementation(() =>
        new Promise(resolve =>
          setTimeout(() => resolve({
            blob: () => Promise.resolve(new Blob(['image data']))
          }), 50)
        )
      );

      const { result } = renderHook(() => useExport(), { wrapper });

      const exportPromise = result.current.exportData({
        format: 'images',
        data: mockImageData,
        filters: mockFilters
      });

      // Progress should be updated during export
      await waitFor(() => {
        expect(result.current.exportProgress).toBeGreaterThan(0);
      }, { timeout: 3000 });

      await exportPromise;

      // Progress should be reset after export
      await waitFor(() => {
        expect(result.current.exportProgress).toBe(0);
      });
    });

    it('handles image download errors gracefully', async () => {
      (global.fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

      const { result } = renderHook(() => useExport(), { wrapper });
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      await result.current.exportData({
        format: 'images',
        data: mockImageData,
        filters: mockFilters
      });

      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith(
          'Failed to download image 1:',
          expect.any(Error)
        );
      });

      consoleSpy.mockRestore();
    });
  });

  describe('Date Range Filtering', () => {
    it('filters by start date', async () => {
      const { result } = renderHook(() => useExport(), { wrapper });

      const dateFilters = {
        ...mockFilters,
        dateRange: { start: '2024-01-02', end: '' }
      };

      await result.current.exportData({
        format: 'json',
        data: mockImageData,
        filters: dateFilters
      });

      await waitFor(() => {
        expect(mockSaveAs).toHaveBeenCalled();
      });

      const savedBlob = (mockSaveAs as any).lastBlob as Blob;
      const jsonContent = await savedBlob.text();
      const parsedData = JSON.parse(jsonContent);

      expect(parsedData.totalRecords).toBe(1); // Only second item
      expect(parsedData.data[0].id).toBe('2');
    });

    it('filters by end date', async () => {
      const { result } = renderHook(() => useExport(), { wrapper });

      const dateFilters = {
        ...mockFilters,
        dateRange: { start: '', end: '2024-01-01' }
      };

      await result.current.exportData({
        format: 'json',
        data: mockImageData,
        filters: dateFilters
      });

      await waitFor(() => {
        expect(mockSaveAs).toHaveBeenCalled();
      });

      const savedBlob = (mockSaveAs as any).lastBlob as Blob;
      const jsonContent = await savedBlob.text();
      const parsedData = JSON.parse(jsonContent);

      expect(parsedData.totalRecords).toBe(1); // Only first item
      expect(parsedData.data[0].id).toBe('1');
    });
  });

  describe('Error Handling', () => {
    it('throws error for unsupported format', async () => {
      const { result } = renderHook(() => useExport(), { wrapper });

      await expect(
        result.current.exportData({
          format: 'unsupported' as any,
          data: mockImageData,
          filters: mockFilters
        })
      ).rejects.toThrow('Unsupported export format: unsupported');
    });

    it('resets loading state after error', async () => {
      const { result } = renderHook(() => useExport(), { wrapper });

      try {
        await result.current.exportData({
          format: 'unsupported' as any,
          data: mockImageData,
          filters: mockFilters
        });
      } catch (error) {
        // Expected error
      }

      await waitFor(() => {
        expect(result.current.isExporting).toBe(false);
      });
    });
  });

  describe('Legacy API Compatibility', () => {
    it('maintains backward compatibility with legacy export', async () => {
      const { result } = renderHook(() => useExport(), { wrapper });

      // Mock API service response
      const mockApiResponse = 'csv,data,here';
      global.fetch = jest.fn().mockResolvedValue({
        text: () => Promise.resolve(mockApiResponse)
      });

      // Mock API service
      const mockApiService = {
        exportResults: jest.fn().mockResolvedValue(mockApiResponse)
      };

      // This would normally be called through the legacy API
      expect(result.current.exportResults).toBeDefined();
      expect(result.current.isExporting).toBe(false);
      expect(result.current.error).toBeNull();
    });
  });
});