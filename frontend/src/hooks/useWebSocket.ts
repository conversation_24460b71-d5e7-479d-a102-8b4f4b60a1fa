import { useEffect, useCallback } from 'react';
import { WebSocketService } from '../services/websocket';

type WebSocketHandler = (data: any) => void;

export function useWebSocket(jobId: string | null, onMessage: WebSocketHandler) {
  const connect = useCallback(
    (ws: WebSocketService) => {
      ws.addMessageHandler(onMessage);
      ws.connect();
    },
    [onMessage]
  );

  useEffect(() => {
    if (!jobId) return;

    const ws = new WebSocketService(jobId);
    connect(ws);

    return () => {
      ws.disconnect();
    };
  }, [jobId, connect]);
}