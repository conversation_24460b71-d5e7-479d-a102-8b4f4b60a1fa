import { useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import { ApiService } from '../services/api';
import { saveAs } from 'file-saver';
import JSZip from 'jszip';
import jsPDF from 'jspdf';
import 'jspdf-autotable';

// Extend jsPDF type to include autoTable
declare module 'jspdf' {
  interface jsPDF {
    autoTable: (options: any) => jsPDF;
  }
}

type ExportFormat = 'csv' | 'json' | 'pdf' | 'images';

interface ExportFilter {
  dateRange: {
    start: string;
    end: string;
  };
  scoreRange: {
    min: number;
    max: number;
  };
  status: string[];
  categories: string[];
}

interface ImageData {
  id: string;
  url: string;
  score: number;
  status: 'approved' | 'rejected' | 'pending';
  category: string;
  processedAt: string;
  processingTime: number;
  metadata?: Record<string, any>;
}

interface ProcessingStats {
  totalProcessed: number;
  successRate: number;
  averageProcessingTime: number;
  totalImages: number;
  approved: number;
  rejected: number;
}

interface QualityMetrics {
  averageScore: number;
  scoreDistribution: Array<{ range: string; count: number }>;
  qualityTrend: Array<{ date: string; score: number }>;
  categoryScores: Array<{ category: string; score: number }>;
}

export function useExport() {
  const [exportProgress, setExportProgress] = useState(0);

  // Legacy export mutation for backward compatibility
  const legacyExportMutation = useMutation({
    mutationFn: async ({ jobId, format }: { jobId: string; format: 'csv' | 'json' }) => {
      const response = await ApiService.exportResults(jobId, format);

      // For CSV format, the download is handled in ApiService, so we return early
      if (format === 'csv' || !response) {
        return;
      }

      // Create a blob from the response data
      const blob = new Blob([response], {
        type: 'application/json',
      });

      // Create a download link and trigger the download
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `product-images-${jobId}.${format}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      return response;
    },
  });

  const filterData = (data: ImageData[], filters: ExportFilter): ImageData[] => {
    return data.filter(item => {
      // Date range filter
      if (filters.dateRange.start || filters.dateRange.end) {
        const itemDate = new Date(item.processedAt);
        if (filters.dateRange.start) {
          const startDate = new Date(filters.dateRange.start + 'T00:00:00Z');
          if (itemDate < startDate) {
            return false;
          }
        }
        if (filters.dateRange.end) {
          const endDate = new Date(filters.dateRange.end + 'T23:59:59Z');
          if (itemDate > endDate) {
            return false;
          }
        }
      }

      // Score range filter
      const scorePercent = item.score * 100;
      if (scorePercent < filters.scoreRange.min || scorePercent > filters.scoreRange.max) {
        return false;
      }

      // Status filter
      if (!filters.status.includes(item.status)) {
        return false;
      }

      // Category filter
      if (!filters.categories.includes(item.category)) {
        return false;
      }

      return true;
    });
  };

  const exportToCSV = async (data: ImageData[], filters: ExportFilter) => {
    const filteredData = filterData(data, filters);

    const headers = [
      'ID',
      'URL',
      'Score (%)',
      'Status',
      'Category',
      'Processed At',
      'Processing Time (ms)',
      'Metadata'
    ];

    const csvContent = [
      headers.join(','),
      ...filteredData.map(item => [
        item.id,
        `"${item.url}"`,
        (item.score * 100).toFixed(2),
        item.status,
        item.category,
        item.processedAt,
        item.processingTime,
        `"${JSON.stringify(item.metadata || {})}"`
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    saveAs(blob, `image-analysis-export-${new Date().toISOString().split('T')[0]}.csv`);
  };

  const exportToJSON = async (data: ImageData[], filters: ExportFilter) => {
    const filteredData = filterData(data, filters);

    const exportData = {
      exportedAt: new Date().toISOString(),
      filters,
      totalRecords: filteredData.length,
      data: filteredData
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json;charset=utf-8;'
    });
    saveAs(blob, `image-analysis-export-${new Date().toISOString().split('T')[0]}.json`);
  };

  const exportToPDF = async (
    data: ImageData[],
    filters: ExportFilter,
    processingStats: ProcessingStats,
    qualityMetrics: QualityMetrics
  ) => {
    const filteredData = filterData(data, filters);
    const doc = new jsPDF();

    // Title
    doc.setFontSize(20);
    doc.text('Image Analysis Report', 20, 20);

    // Export info
    doc.setFontSize(12);
    doc.text(`Generated: ${new Date().toLocaleDateString()}`, 20, 35);
    doc.text(`Total Records: ${filteredData.length}`, 20, 45);

    // Summary statistics
    doc.setFontSize(16);
    doc.text('Summary Statistics', 20, 65);

    doc.setFontSize(12);
    const summaryData = [
      ['Total Images Processed', processingStats.totalProcessed.toString()],
      ['Success Rate', `${processingStats.successRate.toFixed(1)}%`],
      ['Average Processing Time', `${processingStats.averageProcessingTime}ms`],
      ['Average Quality Score', `${(qualityMetrics.averageScore * 100).toFixed(1)}%`],
      ['Approved Images', processingStats.approved.toString()],
      ['Rejected Images', processingStats.rejected.toString()]
    ];

    doc.autoTable({
      startY: 75,
      head: [['Metric', 'Value']],
      body: summaryData,
      theme: 'grid'
    });

    doc.save(`image-analysis-report-${new Date().toISOString().split('T')[0]}.pdf`);
  };

  const exportImages = async (data: ImageData[], filters: ExportFilter) => {
    const filteredData = filterData(data, filters);
    const zip = new JSZip();

    setExportProgress(0);

    for (let i = 0; i < filteredData.length; i++) {
      const item = filteredData[i];
      try {
        const response = await fetch(item.url);
        const blob = await response.blob();

        // Get file extension from URL or default to jpg
        const urlParts = item.url.split('.');
        const extension = urlParts.length > 1 ? urlParts.pop() : 'jpg';

        const fileName = `${item.category}/${item.status}/${item.id}.${extension}`;
        zip.file(fileName, blob);

        setExportProgress(Math.round(((i + 1) / filteredData.length) * 100));
      } catch (error) {
        console.error(`Failed to download image ${item.id}:`, error);
      }
    }

    // Add metadata file
    const metadata = {
      exportedAt: new Date().toISOString(),
      filters,
      totalImages: filteredData.length,
      images: filteredData.map(item => ({
        id: item.id,
        score: item.score,
        status: item.status,
        category: item.category,
        processedAt: item.processedAt,
        processingTime: item.processingTime
      }))
    };

    zip.file('metadata.json', JSON.stringify(metadata, null, 2));

    const zipBlob = await zip.generateAsync({ type: 'blob' });
    saveAs(zipBlob, `image-analysis-images-${new Date().toISOString().split('T')[0]}.zip`);

    setExportProgress(0);
  };

  const advancedExportMutation = useMutation({
    mutationFn: async ({
      format,
      data,
      filters,
      processingStats,
      qualityMetrics
    }: {
      format: ExportFormat;
      data: ImageData[];
      filters: ExportFilter;
      processingStats?: ProcessingStats;
      qualityMetrics?: QualityMetrics;
    }) => {
      switch (format) {
        case 'csv':
          await exportToCSV(data, filters);
          break;
        case 'json':
          await exportToJSON(data, filters);
          break;
        case 'pdf':
          if (processingStats && qualityMetrics) {
            await exportToPDF(data, filters, processingStats, qualityMetrics);
          } else {
            throw new Error('Processing stats and quality metrics required for PDF export');
          }
          break;
        case 'images':
          await exportImages(data, filters);
          break;
        default:
          throw new Error(`Unsupported export format: ${format}`);
      }
    },
  });

  return {
    // Legacy API for backward compatibility
    exportResults: legacyExportMutation.mutate,
    isExporting: legacyExportMutation.isPending || advancedExportMutation.isPending,
    error: legacyExportMutation.error || advancedExportMutation.error,

    // Advanced export API
    exportData: advancedExportMutation.mutateAsync,
    exportProgress,
  };
}