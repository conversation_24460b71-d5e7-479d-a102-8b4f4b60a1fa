import { useCallback } from 'react';
import { useMutation } from '@tanstack/react-query';
import { ApiService } from '../services/api';
import { useAppStore } from '../store';

export function useFileUpload() {
  const setCurrentJobId = useAppStore((state) => state.setCurrentJobId);
  const updateJobState = useAppStore((state) => state.updateJobState);

  const uploadMutation = useMutation({
    mutationFn: (file: File) => ApiService.uploadProducts(file),
    onSuccess: (data) => {
      setCurrentJobId(data.job_id);
      updateJobState({
        status: 'pending',
        progress: 0,
        message: 'Processing started...',
      });
    },
    onError: (error) => {
      updateJobState({
        status: 'failed',
        message: error instanceof Error ? error.message : 'Upload failed',
      });
    },
  });

  const uploadFile = useCallback(
    (file: File) => {
      uploadMutation.mutate(file);
    },
    [uploadMutation]
  );

  return {
    uploadFile,
    isUploading: uploadMutation.isLoading,
    error: uploadMutation.error,
    reset: uploadMutation.reset,
  };
}