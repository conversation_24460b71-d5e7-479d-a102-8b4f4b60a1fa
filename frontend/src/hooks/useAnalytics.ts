import { useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';

interface ImageData {
  id: string;
  score: number;
  processedAt: string;
  status: 'approved' | 'rejected' | 'pending';
  processingTime: number;
  category: string;
}

interface ProcessingStats {
  totalProcessed: number;
  successRate: number;
  averageProcessingTime: number;
  totalImages: number;
  approved: number;
  rejected: number;
}

interface QualityMetrics {
  averageScore: number;
  scoreDistribution: { range: string; count: number }[];
  qualityTrend: { date: string; score: number }[];
  categoryScores: { category: string; score: number }[];
}

interface AnalyticsData {
  processingStats: ProcessingStats;
  qualityMetrics: QualityMetrics;
  isLoading: boolean;
  error: Error | null;
}

const calculateScoreDistribution = (images: ImageData[]) => {
  const ranges = [
    { min: 0, max: 0.2, label: '0-20%' },
    { min: 0.2, max: 0.4, label: '20-40%' },
    { min: 0.4, max: 0.6, label: '40-60%' },
    { min: 0.6, max: 0.8, label: '60-80%' },
    { min: 0.8, max: 1, label: '80-100%' },
  ];

  const distribution = ranges.map(range => ({
    range: range.label,
    count: images.filter(img => img.score >= range.min && img.score < range.max).length,
  }));

  return distribution;
};

const calculateQualityTrend = (images: ImageData[]) => {
  const sortedImages = [...images].sort(
    (a, b) => new Date(a.processedAt).getTime() - new Date(b.processedAt).getTime()
  );

  const trend = sortedImages.reduce<{ date: string; score: number }[]>((acc, img) => {
    const date = new Date(img.processedAt).toLocaleDateString();
    const existingEntry = acc.find(entry => entry.date === date);

    if (existingEntry) {
      existingEntry.score = (existingEntry.score + img.score) / 2;
    } else {
      acc.push({ date, score: img.score });
    }

    return acc;
  }, []);

  return trend;
};

const calculateCategoryScores = (images: ImageData[]) => {
  const categoryMap = images.reduce<Record<string, number[]>>((acc, img) => {
    if (!acc[img.category]) {
      acc[img.category] = [];
    }
    acc[img.category].push(img.score);
    return acc;
  }, {});

  return Object.entries(categoryMap).map(([category, scores]) => ({
    category,
    score: scores.reduce((a, b) => a + b, 0) / scores.length,
  }));
};

export const useAnalytics = (): AnalyticsData => {
  // Fetch image data from API
  const { data: images, isLoading, error } = useQuery({
    queryKey: ['analytics'],
    queryFn: async (): Promise<ImageData[]> => {
      const response = await fetch('/api/analytics/images');
      if (!response.ok) {
        throw new Error('Failed to fetch analytics data');
      }
      return response.json();
    },
    staleTime: 5 * 60 * 1000, // Consider data fresh for 5 minutes
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
  });

  const analytics = useMemo(() => {
    if (!images) {
      return null;
    }

    const approved = images.filter(img => img.status === 'approved').length;
    const rejected = images.filter(img => img.status === 'rejected').length;
    const totalProcessed = approved + rejected;

    const processingStats: ProcessingStats = {
      totalProcessed,
      successRate: totalProcessed > 0 ? (approved / totalProcessed) * 100 : 0,
      averageProcessingTime:
        images.length > 0 ? images.reduce((sum, img) => sum + img.processingTime, 0) / images.length : 0,
      totalImages: images.length,
      approved,
      rejected,
    };

    const qualityMetrics: QualityMetrics = {
      averageScore:
        images.length > 0 ? images.reduce((sum, img) => sum + img.score, 0) / images.length : 0,
      scoreDistribution: calculateScoreDistribution(images),
      qualityTrend: calculateQualityTrend(images),
      categoryScores: calculateCategoryScores(images),
    };

    return {
      processingStats,
      qualityMetrics,
    };
  }, [images]);

  return {
    processingStats: analytics?.processingStats ?? {
      totalProcessed: 0,
      successRate: 0,
      averageProcessingTime: 0,
      totalImages: 0,
      approved: 0,
      rejected: 0,
    },
    qualityMetrics: analytics?.qualityMetrics ?? {
      averageScore: 0,
      scoreDistribution: [],
      qualityTrend: [],
      categoryScores: [],
    },
    isLoading,
    error,
  };
};