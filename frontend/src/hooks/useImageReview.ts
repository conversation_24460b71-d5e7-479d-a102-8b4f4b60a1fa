import { useCallback } from 'react';
import { useAppStore } from '../store';

export function useImageReview(productId: string | null) {
  const products = useAppStore((state) => state.products);
  const updateProductImages = useAppStore((state) => state.updateProductImages);

  const product = productId ? products.find((p) => p.id === productId) : null;

  const acceptImage = useCallback(
    (imageUrl: string) => {
      if (!product) return;

      const updatedImages = product.images.map((img) => {
        if (img.url === imageUrl) {
          return { ...img, status: 'accepted' };
        }
        return img;
      });

      updateProductImages(product.id, updatedImages);
    },
    [product, updateProductImages]
  );

  const rejectImage = useCallback(
    (imageUrl: string) => {
      if (!product) return;

      const updatedImages = product.images.map((img) => {
        if (img.url === imageUrl) {
          return { ...img, status: 'rejected' };
        }
        return img;
      });

      updateProductImages(product.id, updatedImages);
    },
    [product, updateProductImages]
  );

  return {
    product,
    acceptImage,
    rejectImage,
  };
}