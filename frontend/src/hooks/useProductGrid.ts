import { useMemo } from 'react';
import { useAppStore } from '../store';

export function useProductGrid() {
  const products = useAppStore((state) => state.products);
  const setSelectedProductId = useAppStore((state) => state.setSelectedProductId);

  const productStats = useMemo(() => {
    return products.map((product) => {
      const totalImages = product.images.length;
      const acceptedImages = product.images.filter(
        (img) => img.status === 'accepted'
      ).length;
      const rejectedImages = product.images.filter(
        (img) => img.status === 'rejected'
      ).length;
      const pendingImages = totalImages - acceptedImages - rejectedImages;

      const averageQuality =
        product.images.reduce((sum, img) => sum + img.quality, 0) / totalImages;

      return {
        id: product.id,
        name: product.name,
        previewImage: product.images[0]?.url,
        totalImages,
        acceptedImages,
        rejectedImages,
        pendingImages,
        averageQuality,
      };
    });
  }, [products]);

  const selectProduct = (productId: string) => {
    setSelectedProductId(productId);
  };

  return {
    productStats,
    selectProduct,
  };
}