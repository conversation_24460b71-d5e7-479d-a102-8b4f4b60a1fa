import * as DropdownMenu from '@radix-ui/react-dropdown-menu';
import { useAppStore } from '../../store';
import { useExport } from '../../hooks/useExport';

export function ResultsExport() {
  const currentJobId = useAppStore((state) => state.currentJobId);
  const jobState = useAppStore((state) => state.jobState);
  const { exportResults, isExporting, error } = useExport();

  if (!currentJobId || jobState.status !== 'completed') {
    return null;
  }

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">Export Results</h2>

      <div className="flex items-center gap-4">
        <DropdownMenu.Root>
          <DropdownMenu.Trigger asChild>
            <button
              className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 disabled:opacity-50"
              disabled={isExporting}
            >
              {isExporting ? 'Exporting...' : 'Export Results'}
            </button>
          </DropdownMenu.Trigger>

          <DropdownMenu.Portal>
            <DropdownMenu.Content
              className="min-w-[180px] bg-popover text-popover-foreground rounded-md p-1 shadow-md"
              sideOffset={5}
            >
              <DropdownMenu.Item
                className="px-2 py-1.5 text-sm outline-none cursor-pointer rounded hover:bg-accent hover:text-accent-foreground"
                onSelect={() => exportResults({ jobId: currentJobId, format: 'csv' })}
              >
                Export as CSV
              </DropdownMenu.Item>
              <DropdownMenu.Item
                className="px-2 py-1.5 text-sm outline-none cursor-pointer rounded hover:bg-accent hover:text-accent-foreground"
                onSelect={() => exportResults({ jobId: currentJobId, format: 'json' })}
              >
                Export as JSON
              </DropdownMenu.Item>
            </DropdownMenu.Content>
          </DropdownMenu.Portal>
        </DropdownMenu.Root>

        {error && (
          <p className="text-sm text-destructive">
            {error instanceof Error ? error.message : 'Export failed'}
          </p>
        )}
      </div>

      <p className="text-sm text-muted-foreground">
        Export your results in CSV format for spreadsheet applications, or JSON format for
        programmatic use.
      </p>
    </div>
  );
}