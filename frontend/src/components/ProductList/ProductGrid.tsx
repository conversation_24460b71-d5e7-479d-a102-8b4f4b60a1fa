import { useProductGrid } from '../../hooks/useProductGrid';

export function ProductGrid() {
  const { productStats, selectProduct } = useProductGrid();

  if (productStats.length === 0) {
    return (
      <div className="text-center text-muted-foreground">
        No products found
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
      {productStats.map((product) => (
        <button
          key={product.id}
          onClick={() => selectProduct(product.id)}
          className="bg-card rounded-lg p-4 text-left hover:shadow-md transition-shadow"
        >
          <div className="flex items-start gap-4">
            {product.previewImage ? (
              <img
                src={product.previewImage}
                alt={product.name || 'Product'}
                className="w-24 h-24 object-cover rounded-md"
                loading="lazy"
              />
            ) : (
              <div className="w-24 h-24 bg-muted rounded-md flex items-center justify-center text-muted-foreground">
                No image
              </div>
            )}

            <div className="flex-1 space-y-2 min-w-0">
              <h3 className="font-medium truncate">
                {product.name || product.id}
              </h3>

              <div className="space-y-1 text-sm">
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">Total Images</span>
                  <span>{product.totalImages}</span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">Average Quality</span>
                  <span>{Math.round(product.averageQuality * 100)}%</span>
                </div>

                <div className="flex gap-2 text-xs">
                  {product.acceptedImages > 0 && (
                    <span className="px-2 py-0.5 rounded-full bg-primary/10 text-primary">
                      {product.acceptedImages} accepted
                    </span>
                  )}
                  {product.rejectedImages > 0 && (
                    <span className="px-2 py-0.5 rounded-full bg-destructive/10 text-destructive">
                      {product.rejectedImages} rejected
                    </span>
                  )}
                  {product.pendingImages > 0 && (
                    <span className="px-2 py-0.5 rounded-full bg-muted text-muted-foreground">
                      {product.pendingImages} pending
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>
        </button>
      ))}
    </div>
  );
}