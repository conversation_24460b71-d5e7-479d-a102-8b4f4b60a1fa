import { useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { useFileUpload } from '../../hooks/useFileUpload';

export function ProductList() {
  const { uploadFile, isUploading, error, reset } = useFileUpload();

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      if (acceptedFiles.length > 0) {
        uploadFile(acceptedFiles[0]);
      }
    },
    [uploadFile]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/csv': ['.csv'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
    },
    maxFiles: 1,
    disabled: isUploading,
  });

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      <div
        {...getRootProps()}
        className={`
          border-2 border-dashed rounded-lg p-8
          transition-colors duration-200 ease-in-out
          ${isDragActive ? 'border-primary bg-primary/5' : 'border-gray-300'}
          ${isUploading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        `}
      >
        <input {...getInputProps()} />
        <div className="text-center space-y-4">
          <div className="text-4xl">📄</div>
          {isDragActive ? (
            <p className="text-lg">Drop the file here...</p>
          ) : (
            <>
              <p className="text-lg font-medium">
                Drag & drop your product list file here
              </p>
              <p className="text-sm text-muted-foreground">
                Supported formats: CSV, XLSX
              </p>
            </>
          )}
        </div>
      </div>

      {error && (
        <div
          className="bg-destructive/10 text-destructive p-4 rounded-md flex items-center justify-between"
          role="alert"
        >
          <p>{error instanceof Error ? error.message : 'Upload failed'}</p>
          <button
            onClick={() => reset()}
            className="text-sm underline hover:no-underline"
          >
            Try again
          </button>
        </div>
      )}

      {isUploading && (
        <div className="text-center text-sm text-muted-foreground">
          Uploading and processing your file...
        </div>
      )}
    </div>
  );
}