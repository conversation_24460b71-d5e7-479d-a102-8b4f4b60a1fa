import { useQuery } from '@tanstack/react-query';
import { ApiService } from '../../services/api';
import { useAppStore } from '../../store';
import { useWebSocket } from '../../hooks/useWebSocket';

export function ProcessingDashboard() {
  const currentJobId = useAppStore((state) => state.currentJobId);
  const jobState = useAppStore((state) => state.jobState);
  const updateJobState = useAppStore((state) => state.updateJobState);
  const setProducts = useAppStore((state) => state.setProducts);

  // WebSocket connection for real-time updates
  useWebSocket(currentJobId, (data) => {
    updateJobState({
      status: data.status,
      progress: data.progress,
      message: data.message,
    });
  });

  // Poll job status as a fallback
  const { data: jobStatus } = useQuery({
    queryKey: ['jobStatus', currentJobId],
    queryFn: () => {
      if (!currentJobId) throw new Error('No job ID');
      return ApiService.getJobStatus(currentJobId);
    },
    enabled: !!currentJobId && jobState.status !== 'completed',
    refetchInterval: 5000, // Poll every 5 seconds as fallback
  });

  // Fetch results when job is completed
  const { data: results } = useQuery({
    queryKey: ['jobResults', currentJobId],
    queryFn: () => {
      if (!currentJobId) throw new Error('No job ID');
      return ApiService.getJobResults(currentJobId);
    },
    enabled: !!currentJobId && jobState.status === 'completed',
    onSuccess: (data) => {
      setProducts(data.products);
    },
  });

  if (!currentJobId) {
    return null;
  }

  return (
    <div className="p-6 space-y-4">
      <div className="bg-card rounded-lg p-6 shadow-sm">
        <h2 className="text-xl font-semibold mb-4">Processing Status</h2>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">
              Status: {jobState.status.charAt(0).toUpperCase() + jobState.status.slice(1)}
            </span>
            <span className="text-sm text-muted-foreground">
              {Math.round(jobState.progress)}%
            </span>
          </div>

          <div className="relative h-2 bg-secondary rounded-full overflow-hidden">
            <div
              className="absolute left-0 top-0 h-full bg-primary transition-all duration-300"
              style={{ width: `${jobState.progress}%` }}
            />
          </div>

          {jobState.message && (
            <p className="text-sm text-muted-foreground">{jobState.message}</p>
          )}

          {jobState.status === 'failed' && (
            <div className="mt-4 p-4 bg-destructive/10 text-destructive rounded-md">
              {jobState.message || 'An error occurred during processing'}
            </div>
          )}
        </div>
      </div>

      {results && (
        <div className="bg-card rounded-lg p-6 shadow-sm">
          <h2 className="text-xl font-semibold mb-4">Results Summary</h2>
          <p className="text-sm text-muted-foreground">
            Found images for {results.products.length} products
          </p>
        </div>
      )}
    </div>
  );
}