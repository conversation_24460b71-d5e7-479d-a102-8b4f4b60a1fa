import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import AdvancedExportOptions from '../AdvancedExportOptions';

describe('AdvancedExportOptions', () => {
  const mockOnExport = jest.fn();
  const mockCategories = ['Product', 'Lifestyle', 'Detail'];

  const defaultProps = {
    onExport: mockOnExport,
    isExporting: false,
    availableCategories: mockCategories
  };

  beforeEach(() => {
    mockOnExport.mockClear();
  });

  it('renders export format options', () => {
    render(<AdvancedExportOptions {...defaultProps} />);

    expect(screen.getByText('CSV')).toBeInTheDocument();
    expect(screen.getByText('JSON')).toBeInTheDocument();
    expect(screen.getByText('PDF Report')).toBeInTheDocument();
    expect(screen.getByText('Image Archive')).toBeInTheDocument();
  });

  it('selects CSV format by default', () => {
    render(<AdvancedExportOptions {...defaultProps} />);

    const csvOption = screen.getByRole('radio', { name: /csv/i });
    expect(csvOption).toBeChecked();
  });

  it('allows format selection', async () => {
    const user = userEvent.setup();
    render(<AdvancedExportOptions {...defaultProps} />);

    const jsonOption = screen.getByRole('radio', { name: /json/i });
    await user.click(jsonOption);

    expect(jsonOption).toBeChecked();
  });

  it('shows/hides filters when toggle is clicked', async () => {
    const user = userEvent.setup();
    render(<AdvancedExportOptions {...defaultProps} />);

    const filterToggle = screen.getByText('Show Filters');
    await user.click(filterToggle);

    expect(screen.getByText('Export Filters')).toBeInTheDocument();
    expect(screen.getByText('Date Range')).toBeInTheDocument();
    expect(screen.getByText('Quality Score Range')).toBeInTheDocument();
    expect(screen.getByText('Status')).toBeInTheDocument();
    expect(screen.getByText('Categories')).toBeInTheDocument();

    const hideFiltersButton = screen.getByText('Hide Filters');
    await user.click(hideFiltersButton);

    expect(screen.queryByText('Export Filters')).not.toBeInTheDocument();
  });

  it('renders all status options when filters are shown', async () => {
    const user = userEvent.setup();
    render(<AdvancedExportOptions {...defaultProps} />);

    await user.click(screen.getByText('Show Filters'));

    expect(screen.getByText('Approved')).toBeInTheDocument();
    expect(screen.getByText('Rejected')).toBeInTheDocument();
    expect(screen.getByText('Pending')).toBeInTheDocument();
  });

  it('renders all available categories when filters are shown', async () => {
    const user = userEvent.setup();
    render(<AdvancedExportOptions {...defaultProps} />);

    await user.click(screen.getByText('Show Filters'));

    mockCategories.forEach(category => {
      expect(screen.getByText(category)).toBeInTheDocument();
    });
  });

  it('updates date range filters', async () => {
    const user = userEvent.setup();
    render(<AdvancedExportOptions {...defaultProps} />);

    await user.click(screen.getByText('Show Filters'));

    const startDateInput = screen.getByPlaceholderText('Start date');
    const endDateInput = screen.getByPlaceholderText('End date');

    await user.type(startDateInput, '2024-01-01');
    await user.type(endDateInput, '2024-01-31');

    expect(startDateInput).toHaveValue('2024-01-01');
    expect(endDateInput).toHaveValue('2024-01-31');
  });

  it('updates score range filters', async () => {
    const user = userEvent.setup();
    render(<AdvancedExportOptions {...defaultProps} />);

    await user.click(screen.getByText('Show Filters'));

    const minScoreInput = screen.getByPlaceholderText('Min score');
    const maxScoreInput = screen.getByPlaceholderText('Max score');

    await user.clear(minScoreInput);
    await user.type(minScoreInput, '20');
    await user.clear(maxScoreInput);
    await user.type(maxScoreInput, '80');

    expect(minScoreInput).toHaveValue(20);
    expect(maxScoreInput).toHaveValue(80);
  });

  it('toggles status filters', async () => {
    const user = userEvent.setup();
    render(<AdvancedExportOptions {...defaultProps} />);

    await user.click(screen.getByText('Show Filters'));

    const approvedCheckbox = screen.getByRole('checkbox', { name: /approved/i });
    expect(approvedCheckbox).toBeChecked();

    await user.click(approvedCheckbox);
    expect(approvedCheckbox).not.toBeChecked();

    await user.click(approvedCheckbox);
    expect(approvedCheckbox).toBeChecked();
  });

  it('toggles category filters', async () => {
    const user = userEvent.setup();
    render(<AdvancedExportOptions {...defaultProps} />);

    await user.click(screen.getByText('Show Filters'));

    const productCheckbox = screen.getByRole('checkbox', { name: /product/i });
    expect(productCheckbox).toBeChecked();

    await user.click(productCheckbox);
    expect(productCheckbox).not.toBeChecked();
  });

  it('resets filters when reset button is clicked', async () => {
    const user = userEvent.setup();
    render(<AdvancedExportOptions {...defaultProps} />);

    await user.click(screen.getByText('Show Filters'));

    // Modify some filters
    const minScoreInput = screen.getByPlaceholderText('Min score');
    await user.clear(minScoreInput);
    await user.type(minScoreInput, '50');

    const approvedCheckbox = screen.getByRole('checkbox', { name: /approved/i });
    await user.click(approvedCheckbox);

    // Reset filters
    await user.click(screen.getByText('Reset Filters'));

    expect(minScoreInput).toHaveValue(0);
    expect(approvedCheckbox).toBeChecked();
  });

  it('calls onExport with correct parameters when export button is clicked', async () => {
    const user = userEvent.setup();
    render(<AdvancedExportOptions {...defaultProps} />);

    await user.click(screen.getByText('Export Data'));

    expect(mockOnExport).toHaveBeenCalledWith('csv', expect.objectContaining({
      dateRange: { start: '', end: '' },
      scoreRange: { min: 0, max: 100 },
      status: ['approved', 'rejected', 'pending'],
      categories: mockCategories
    }));
  });

  it('calls onExport with filtered parameters', async () => {
    const user = userEvent.setup();
    render(<AdvancedExportOptions {...defaultProps} />);

    // Select JSON format
    const jsonOption = screen.getByRole('radio', { name: /json/i });
    await user.click(jsonOption);

    // Show filters and modify them
    await user.click(screen.getByText('Show Filters'));

    const startDateInput = screen.getByPlaceholderText('Start date');
    await user.type(startDateInput, '2024-01-01');

    const minScoreInput = screen.getByPlaceholderText('Min score');
    await user.clear(minScoreInput);
    await user.type(minScoreInput, '30');

    const rejectedCheckbox = screen.getByRole('checkbox', { name: /rejected/i });
    await user.click(rejectedCheckbox);

    await user.click(screen.getByText('Export Data'));

    expect(mockOnExport).toHaveBeenCalledWith('json', expect.objectContaining({
      dateRange: { start: '2024-01-01', end: '' },
      scoreRange: { min: 30, max: 100 },
      status: ['approved', 'pending'], // rejected unchecked
      categories: mockCategories
    }));
  });

  it('disables export button when exporting', () => {
    render(<AdvancedExportOptions {...defaultProps} isExporting={true} />);

    const exportButton = screen.getByRole('button', { name: /exporting/i });
    expect(exportButton).toBeDisabled();
    expect(exportButton).toHaveTextContent('Exporting...');
  });

  it('shows correct format descriptions', () => {
    render(<AdvancedExportOptions {...defaultProps} />);

    expect(screen.getByText('Comma-separated values for spreadsheet analysis')).toBeInTheDocument();
    expect(screen.getByText('Structured data for API integration')).toBeInTheDocument();
    expect(screen.getByText('Formatted report with charts and analytics')).toBeInTheDocument();
    expect(screen.getByText('ZIP file containing filtered images')).toBeInTheDocument();
  });

  it('maintains accessibility with proper labels and roles', () => {
    render(<AdvancedExportOptions {...defaultProps} />);

    // Check radio group accessibility
    const csvRadio = screen.getByRole('radio', { name: /csv/i });
    const jsonRadio = screen.getByRole('radio', { name: /json/i });
    const pdfRadio = screen.getByRole('radio', { name: /pdf report/i });
    const imagesRadio = screen.getByRole('radio', { name: /image archive/i });

    expect(csvRadio).toBeInTheDocument();
    expect(jsonRadio).toBeInTheDocument();
    expect(pdfRadio).toBeInTheDocument();
    expect(imagesRadio).toBeInTheDocument();

    // Check button accessibility
    const exportButton = screen.getByRole('button', { name: /export data/i });
    expect(exportButton).toBeInTheDocument();
  });
});