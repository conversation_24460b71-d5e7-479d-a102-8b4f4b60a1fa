import React, { useState } from 'react';
import { Download, FileText, Database, Image, Calendar, Filter } from 'lucide-react';

interface ExportFilter {
  dateRange: {
    start: string;
    end: string;
  };
  scoreRange: {
    min: number;
    max: number;
  };
  status: string[];
  categories: string[];
}

interface AdvancedExportOptionsProps {
  onExport: (format: string, filters: ExportFilter) => void;
  isExporting: boolean;
  availableCategories: string[];
}

const AdvancedExportOptions: React.FC<AdvancedExportOptionsProps> = ({
  onExport,
  isExporting,
  availableCategories
}) => {
  const [selectedFormat, setSelectedFormat] = useState<string>('csv');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<ExportFilter>({
    dateRange: {
      start: '',
      end: ''
    },
    scoreRange: {
      min: 0,
      max: 100
    },
    status: ['approved', 'rejected', 'pending'],
    categories: availableCategories
  });

  const exportFormats = [
    {
      id: 'csv',
      name: 'CSV',
      description: 'Comma-separated values for spreadsheet analysis',
      icon: Database,
      color: 'text-green-600'
    },
    {
      id: 'json',
      name: 'JSON',
      description: 'Structured data for API integration',
      icon: FileText,
      color: 'text-blue-600'
    },
    {
      id: 'pdf',
      name: 'PDF Report',
      description: 'Formatted report with charts and analytics',
      icon: FileText,
      color: 'text-red-600'
    },
    {
      id: 'images',
      name: 'Image Archive',
      description: 'ZIP file containing filtered images',
      icon: Image,
      color: 'text-purple-600'
    }
  ];

  const statusOptions = [
    { id: 'approved', label: 'Approved', color: 'text-green-600' },
    { id: 'rejected', label: 'Rejected', color: 'text-red-600' },
    { id: 'pending', label: 'Pending', color: 'text-yellow-600' }
  ];

  const handleStatusChange = (status: string, checked: boolean) => {
    setFilters(prev => ({
      ...prev,
      status: checked
        ? [...prev.status, status]
        : prev.status.filter(s => s !== status)
    }));
  };

  const handleCategoryChange = (category: string, checked: boolean) => {
    setFilters(prev => ({
      ...prev,
      categories: checked
        ? [...prev.categories, category]
        : prev.categories.filter(c => c !== category)
    }));
  };

  const handleExport = () => {
    onExport(selectedFormat, filters);
  };

  const resetFilters = () => {
    setFilters({
      dateRange: { start: '', end: '' },
      scoreRange: { min: 0, max: 100 },
      status: ['approved', 'rejected', 'pending'],
      categories: availableCategories
    });
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Export Data</h3>
        <button
          onClick={() => setShowFilters(!showFilters)}
          className="flex items-center gap-2 px-3 py-2 text-sm text-gray-600 hover:text-gray-900 border rounded-md hover:bg-gray-50"
        >
          <Filter className="w-4 h-4" />
          {showFilters ? 'Hide Filters' : 'Show Filters'}
        </button>
      </div>

      {/* Export Format Selection */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Export Format
        </label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {exportFormats.map((format) => {
            const IconComponent = format.icon;
            return (
              <label
                key={format.id}
                className={`relative flex items-start p-4 border rounded-lg cursor-pointer hover:bg-gray-50 ${
                  selectedFormat === format.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200'
                }`}
              >
                <input
                  type="radio"
                  name="exportFormat"
                  value={format.id}
                  checked={selectedFormat === format.id}
                  onChange={(e) => setSelectedFormat(e.target.value)}
                  className="sr-only"
                />
                <div className="flex items-start gap-3">
                  <IconComponent className={`w-5 h-5 mt-0.5 ${format.color}`} />
                  <div>
                    <div className="font-medium text-gray-900">{format.name}</div>
                    <div className="text-sm text-gray-500">{format.description}</div>
                  </div>
                </div>
              </label>
            );
          })}
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center justify-between mb-4">
            <h4 className="font-medium text-gray-900">Export Filters</h4>
            <button
              onClick={resetFilters}
              className="text-sm text-blue-600 hover:text-blue-800"
            >
              Reset Filters
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Date Range */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Calendar className="w-4 h-4 inline mr-1" />
                Date Range
              </label>
              <div className="grid grid-cols-2 gap-2">
                <input
                  type="date"
                  value={filters.dateRange.start}
                  onChange={(e) => setFilters(prev => ({
                    ...prev,
                    dateRange: { ...prev.dateRange, start: e.target.value }
                  }))}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm"
                  placeholder="Start date"
                />
                <input
                  type="date"
                  value={filters.dateRange.end}
                  onChange={(e) => setFilters(prev => ({
                    ...prev,
                    dateRange: { ...prev.dateRange, end: e.target.value }
                  }))}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm"
                  placeholder="End date"
                />
              </div>
            </div>

            {/* Score Range */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Quality Score Range
              </label>
              <div className="grid grid-cols-2 gap-2">
                <input
                  type="number"
                  min="0"
                  max="100"
                  value={filters.scoreRange.min}
                  onChange={(e) => setFilters(prev => ({
                    ...prev,
                    scoreRange: { ...prev.scoreRange, min: Number(e.target.value) }
                  }))}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm"
                  placeholder="Min score"
                />
                <input
                  type="number"
                  min="0"
                  max="100"
                  value={filters.scoreRange.max}
                  onChange={(e) => setFilters(prev => ({
                    ...prev,
                    scoreRange: { ...prev.scoreRange, max: Number(e.target.value) }
                  }))}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm"
                  placeholder="Max score"
                />
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
            {/* Status Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Status
              </label>
              <div className="space-y-2">
                {statusOptions.map((status) => (
                  <label key={status.id} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={filters.status.includes(status.id)}
                      onChange={(e) => handleStatusChange(status.id, e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className={`ml-2 text-sm ${status.color}`}>
                      {status.label}
                    </span>
                  </label>
                ))}
              </div>
            </div>

            {/* Category Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Categories
              </label>
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {availableCategories.map((category) => (
                  <label key={category} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={filters.categories.includes(category)}
                      onChange={(e) => handleCategoryChange(category, e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">
                      {category}
                    </span>
                  </label>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Export Button */}
      <div className="flex justify-end">
        <button
          onClick={handleExport}
          disabled={isExporting}
          className="flex items-center gap-2 px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <Download className="w-4 h-4" />
          {isExporting ? 'Exporting...' : 'Export Data'}
        </button>
      </div>
    </div>
  );
};

export default AdvancedExportOptions;