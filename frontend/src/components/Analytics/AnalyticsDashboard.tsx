import React, { useMemo } from 'react';
import { <PERSON><PERSON><PERSON>, Bar, <PERSON>Axis, <PERSON>Axis, CartesianGrid, <PERSON>lt<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Line, ResponsiveContainer } from 'recharts';
import { ArrowUp, ArrowDown, Clock, Image, CheckCircle, XCircle } from 'lucide-react';

interface ProcessingStats {
  totalProcessed: number;
  successRate: number;
  averageProcessingTime: number;
  totalImages: number;
  approved: number;
  rejected: number;
}

interface QualityMetrics {
  averageScore: number;
  scoreDistribution: { range: string; count: number }[];
  qualityTrend: { date: string; score: number }[];
  categoryScores: { category: string; score: number }[];
}

interface Props {
  processingStats: ProcessingStats;
  qualityMetrics: QualityMetrics;
}

const StatCard: React.FC<{
  title: string;
  value: number | string;
  icon: React.ReactNode;
  trend?: number;
}> = ({ title, value, icon, trend }) => (
  <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
    <div className="flex items-center justify-between mb-4">
      <h3 className="text-gray-600 text-sm font-medium">{title}</h3>
      <div className="text-gray-400">{icon}</div>
    </div>
    <div className="flex items-end justify-between">
      <div className="text-2xl font-semibold text-gray-800">{value}</div>
      {trend !== undefined && (
        <div className={`flex items-center ${trend >= 0 ? 'text-green-500' : 'text-red-500'}`}>
          {trend >= 0 ? <ArrowUp size={16} /> : <ArrowDown size={16} />}
          <span className="ml-1 text-sm">{Math.abs(trend)}%</span>
        </div>
      )}
    </div>
  </div>
);

const AnalyticsDashboard: React.FC<Props> = ({ processingStats, qualityMetrics }) => {
  const formattedProcessingTime = useMemo(() => {
    const minutes = Math.floor(processingStats.averageProcessingTime / 60);
    const seconds = Math.round(processingStats.averageProcessingTime % 60);
    return `${minutes}m ${seconds}s`;
  }, [processingStats.averageProcessingTime]);

  return (
    <div className="space-y-8">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Images"
          value={processingStats.totalImages}
          icon={<Image size={20} />}
        />
        <StatCard
          title="Success Rate"
          value={`${processingStats.successRate}%`}
          icon={<CheckCircle size={20} />}
          trend={5.2} // Example trend
        />
        <StatCard
          title="Avg. Processing Time"
          value={formattedProcessingTime}
          icon={<Clock size={20} />}
          trend={-2.1} // Example trend
        />
        <StatCard
          title="Rejection Rate"
          value={`${((processingStats.rejected / processingStats.totalImages) * 100).toFixed(1)}%`}
          icon={<XCircle size={20} />}
        />
      </div>

      {/* Quality Score Distribution */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
        <h3 className="text-lg font-semibold text-gray-800 mb-6">Quality Score Distribution</h3>
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={qualityMetrics.scoreDistribution}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="range" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Bar dataKey="count" fill="#6366f1" />
          </BarChart>
        </ResponsiveContainer>
      </div>

      {/* Quality Trend Over Time */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
        <h3 className="text-lg font-semibold text-gray-800 mb-6">Quality Trend</h3>
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={qualityMetrics.qualityTrend}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" />
            <YAxis domain={[0, 1]} />
            <Tooltip />
            <Legend />
            <Line
              type="monotone"
              dataKey="score"
              stroke="#6366f1"
              strokeWidth={2}
              dot={false}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>

      {/* Category Scores */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-100">
        <h3 className="text-lg font-semibold text-gray-800 mb-6">Category Performance</h3>
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={qualityMetrics.categoryScores} layout="vertical">
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis type="number" domain={[0, 1]} />
            <YAxis dataKey="category" type="category" />
            <Tooltip />
            <Legend />
            <Bar dataKey="score" fill="#6366f1" />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default AnalyticsDashboard;