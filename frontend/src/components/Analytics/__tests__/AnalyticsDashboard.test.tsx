import React from 'react';
import { render, screen } from '@testing-library/react';
import AnalyticsDashboard from '../AnalyticsDashboard';

describe('AnalyticsDashboard', () => {
  const mockProcessingStats = {
    totalProcessed: 1000,
    successRate: 85.5,
    averageProcessingTime: 145, // 2m 25s
    totalImages: 1200,
    approved: 855,
    rejected: 145
  };

  const mockQualityMetrics = {
    averageScore: 0.82,
    scoreDistribution: [
      { range: '0-20%', count: 50 },
      { range: '20-40%', count: 100 },
      { range: '40-60%', count: 200 },
      { range: '60-80%', count: 400 },
      { range: '80-100%', count: 450 }
    ],
    qualityTrend: [
      { date: '2024-01-01', score: 0.75 },
      { date: '2024-01-02', score: 0.78 },
      { date: '2024-01-03', score: 0.82 }
    ],
    categoryScores: [
      { category: 'Product', score: 0.85 },
      { category: 'Lifestyle', score: 0.78 },
      { category: 'Detail', score: 0.82 }
    ]
  };

  it('renders all key metric cards', () => {
    render(
      <AnalyticsDashboard
        processingStats={mockProcessingStats}
        qualityMetrics={mockQualityMetrics}
      />
    );

    expect(screen.getByText('Total Images')).toBeInTheDocument();
    expect(screen.getByText('1200')).toBeInTheDocument();

    expect(screen.getByText('Success Rate')).toBeInTheDocument();
    expect(screen.getByText('85.5%')).toBeInTheDocument();

    expect(screen.getByText('Avg. Processing Time')).toBeInTheDocument();
    expect(screen.getByText('2m 25s')).toBeInTheDocument();

    expect(screen.getByText('Rejection Rate')).toBeInTheDocument();
    expect(screen.getByText('12.1%')).toBeInTheDocument();
  });

  it('renders quality score distribution chart', () => {
    render(
      <AnalyticsDashboard
        processingStats={mockProcessingStats}
        qualityMetrics={mockQualityMetrics}
      />
    );

    expect(screen.getByText('Quality Score Distribution')).toBeInTheDocument();
    mockQualityMetrics.scoreDistribution.forEach(item => {
      expect(screen.getByText(item.range)).toBeInTheDocument();
    });
  });

  it('renders quality trend chart', () => {
    render(
      <AnalyticsDashboard
        processingStats={mockProcessingStats}
        qualityMetrics={mockQualityMetrics}
      />
    );

    expect(screen.getByText('Quality Trend')).toBeInTheDocument();
    mockQualityMetrics.qualityTrend.forEach(item => {
      expect(screen.getByText(item.date)).toBeInTheDocument();
    });
  });

  it('renders category performance chart', () => {
    render(
      <AnalyticsDashboard
        processingStats={mockProcessingStats}
        qualityMetrics={mockQualityMetrics}
      />
    );

    expect(screen.getByText('Category Performance')).toBeInTheDocument();
    mockQualityMetrics.categoryScores.forEach(item => {
      expect(screen.getByText(item.category)).toBeInTheDocument();
    });
  });

  it('displays trend indicators correctly', () => {
    render(
      <AnalyticsDashboard
        processingStats={mockProcessingStats}
        qualityMetrics={mockQualityMetrics}
      />
    );

    // Positive trend
    const positiveValue = screen.getByText('5.2%');
    expect(positiveValue.parentElement).toHaveClass('text-green-500');

    // Negative trend
    const negativeValue = screen.getByText('2.1%');
    expect(negativeValue.parentElement).toHaveClass('text-red-500');
  });

  it('handles zero values gracefully', () => {
    const emptyStats = {
      totalProcessed: 0,
      successRate: 0,
      averageProcessingTime: 0,
      totalImages: 0,
      approved: 0,
      rejected: 0
    };

    render(
      <AnalyticsDashboard
        processingStats={emptyStats}
        qualityMetrics={{
          ...mockQualityMetrics,
          scoreDistribution: [],
          qualityTrend: [],
          categoryScores: []
        }}
      />
    );

    expect(screen.getByText('0')).toBeInTheDocument();
    expect(screen.getByText('0%')).toBeInTheDocument();
    expect(screen.getByText('0m 0s')).toBeInTheDocument();
  });

  it('maintains responsive layout', () => {
    const { container } = render(
      <AnalyticsDashboard
        processingStats={mockProcessingStats}
        qualityMetrics={mockQualityMetrics}
      />
    );

    const metricsGrid = container.querySelector('.grid');
    expect(metricsGrid).toHaveClass(
      'grid-cols-1',
      'md:grid-cols-2',
      'lg:grid-cols-4'
    );
  });
});