import { Link, useLocation } from 'react-router-dom';
import { cn } from '../../utils/cn';

export function Navigation() {
  const location = useLocation();

  const links = [
    { href: '/', label: 'Home' },
    { href: '/products', label: 'Products' },
    { href: '/review', label: 'Review' },
    { href: '/analytics', label: 'Analytics' },
  ];

  return (
    <nav className="flex space-x-4">
      {links.map((link) => (
        <Link
          key={link.href}
          to={link.href}
          className={cn(
            'px-3 py-2 rounded-md text-sm font-medium',
            location.pathname === link.href
              ? 'bg-primary text-primary-foreground'
              : 'text-muted-foreground hover:text-foreground hover:bg-accent'
          )}
        >
          {link.label}
        </Link>
      ))}
    </nav>
  );
}