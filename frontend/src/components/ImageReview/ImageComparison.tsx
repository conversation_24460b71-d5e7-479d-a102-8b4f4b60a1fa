import { useState } from 'react';
import { ChevronLeft, ChevronRight, ZoomIn, ZoomOut } from 'lucide-react';

interface ImageComparisonProps {
  beforeImage: string;
  afterImage: string;
  beforeLabel?: string;
  afterLabel?: string;
}

export function ImageComparison({
  beforeImage,
  afterImage,
  beforeLabel = 'Before',
  afterLabel = 'After'
}: ImageComparisonProps) {
  const [zoom, setZoom] = useState(1);
  const [showBefore, setShowBefore] = useState(true);

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev + 0.25, 3));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev - 0.25, 0.5));
  };

  const toggleView = () => {
    setShowBefore(prev => !prev);
  };

  return (
    <div className="relative w-full h-[500px] bg-background border rounded-lg overflow-hidden">
      <div className="absolute top-4 right-4 flex items-center gap-2 z-10">
        <button
          onClick={handleZoomOut}
          className="p-2 bg-background/80 backdrop-blur-sm rounded-full hover:bg-background/90 transition-colors"
          aria-label="Zoom out"
        >
          <ZoomOut className="w-4 h-4" />
        </button>
        <button
          onClick={handleZoomIn}
          className="p-2 bg-background/80 backdrop-blur-sm rounded-full hover:bg-background/90 transition-colors"
          aria-label="Zoom in"
        >
          <ZoomIn className="w-4 h-4" />
        </button>
      </div>

      <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex items-center gap-2 z-10">
        <button
          onClick={toggleView}
          className="flex items-center gap-2 px-4 py-2 bg-background/80 backdrop-blur-sm rounded-full hover:bg-background/90 transition-colors"
        >
          <ChevronLeft className={`w-4 h-4 transition-opacity ${showBefore ? 'opacity-100' : 'opacity-0'}`} />
          <span>{showBefore ? beforeLabel : afterLabel}</span>
          <ChevronRight className={`w-4 h-4 transition-opacity ${!showBefore ? 'opacity-100' : 'opacity-0'}`} />
        </button>
      </div>

      <div className="relative w-full h-full overflow-hidden">
        <div
          className="absolute inset-0 transition-transform duration-300 ease-in-out"
          style={{
            transform: `scale(${zoom}) ${showBefore ? 'translateX(0)' : 'translateX(-100%)'}`
          }}
        >
          <img
            src={beforeImage}
            alt={beforeLabel}
            className="w-full h-full object-contain"
          />
        </div>
        <div
          className="absolute inset-0 transition-transform duration-300 ease-in-out"
          style={{
            transform: `scale(${zoom}) ${showBefore ? 'translateX(100%)' : 'translateX(0)'}`
          }}
        >
          <img
            src={afterImage}
            alt={afterLabel}
            className="w-full h-full object-contain"
          />
        </div>
      </div>
    </div>
  );
}