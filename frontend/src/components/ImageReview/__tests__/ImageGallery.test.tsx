import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { useVirtualizer } from '@tanstack/react-virtual';
import ImageGallery from '../ImageGallery';

// Mock @tanstack/react-virtual
jest.mock('@tanstack/react-virtual', () => ({
  useVirtualizer: jest.fn().mockReturnValue({
    getVirtualItems: () => [
      { index: 0, start: 0, end: 100, size: 100 },
      { index: 1, start: 100, end: 200, size: 100 },
    ],
    getTotalSize: () => 200,
    scrollToIndex: jest.fn(),
    getScrollElement: () => null,
  }),
}));

describe('ImageGallery', () => {
  const mockImages = [
    { id: '1', url: 'image1.jpg', score: 0.8, selected: false },
    { id: '2', url: 'image2.jpg', score: 0.9, selected: false },
    { id: '3', url: 'image3.jpg', score: 0.7, selected: false },
  ];

  const mockHandlers = {
    onSelect: jest.fn(),
    onApprove: jest.fn(),
    onReject: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders virtual grid of images', () => {
    render(
      <ImageGallery
        images={mockImages}
        selectedIds={[]}
        {...mockHandlers}
      />
    );

    expect(useVirtualizer).toHaveBeenCalled();
    expect(screen.getAllByRole('img')).toHaveLength(2); // Virtual items length
  });

  it('handles image selection', () => {
    render(
      <ImageGallery
        images={mockImages}
        selectedIds={[]}
        {...mockHandlers}
      />
    );

    const firstImage = screen.getAllByRole('img')[0];
    fireEvent.click(firstImage);

    expect(mockHandlers.onSelect).toHaveBeenCalledWith(['1']);
  });

  it('supports multi-selection with Ctrl/Cmd key', () => {
    render(
      <ImageGallery
        images={mockImages}
        selectedIds={['1']}
        {...mockHandlers}
      />
    );

    const secondImage = screen.getAllByRole('img')[1];
    fireEvent.click(secondImage, { ctrlKey: true });

    expect(mockHandlers.onSelect).toHaveBeenCalledWith(['1', '2']);
  });

  it('supports range selection with Shift key', () => {
    render(
      <ImageGallery
        images={mockImages}
        selectedIds={['1']}
        {...mockHandlers}
      />
    );

    const lastImage = screen.getAllByRole('img')[1];
    fireEvent.click(lastImage, { shiftKey: true });

    expect(mockHandlers.onSelect).toHaveBeenCalledWith(['1', '2']);
  });

  it('handles keyboard navigation', () => {
    render(
      <ImageGallery
        images={mockImages}
        selectedIds={['1']}
        {...mockHandlers}
      />
    );

    const gallery = screen.getByRole('grid');

    // Arrow right
    fireEvent.keyDown(gallery, { key: 'ArrowRight' });
    expect(mockHandlers.onSelect).toHaveBeenCalledWith(['2']);

    // Arrow left
    fireEvent.keyDown(gallery, { key: 'ArrowLeft' });
    expect(mockHandlers.onSelect).toHaveBeenCalledWith(['1']);
  });

  it('handles keyboard shortcuts for actions', () => {
    render(
      <ImageGallery
        images={mockImages}
        selectedIds={['1']}
        {...mockHandlers}
      />
    );

    const gallery = screen.getByRole('grid');

    // Approve with 'a' key
    fireEvent.keyDown(gallery, { key: 'a' });
    expect(mockHandlers.onApprove).toHaveBeenCalledWith(['1']);

    // Reject with 'r' key
    fireEvent.keyDown(gallery, { key: 'r' });
    expect(mockHandlers.onReject).toHaveBeenCalledWith(['1']);
  });

  it('calculates optimal column count based on container width', () => {
    const { rerender } = render(
      <ImageGallery
        images={mockImages}
        selectedIds={[]}
        {...mockHandlers}
      />
    );

    // Mock different container widths
    Object.defineProperty(HTMLElement.prototype, 'offsetWidth', {
      configurable: true,
      value: 800,
    });

    rerender(
      <ImageGallery
        images={mockImages}
        selectedIds={[]}
        {...mockHandlers}
      />
    );

    const gallery = screen.getByRole('grid');
    expect(gallery).toHaveStyle({ gridTemplateColumns: 'repeat(4, 1fr)' });
  });

  it('displays loading state for images', () => {
    render(
      <ImageGallery
        images={[{ ...mockImages[0], url: '' }]}
        selectedIds={[]}
        {...mockHandlers}
      />
    );

    expect(screen.getByTestId('image-skeleton')).toBeInTheDocument();
  });

  it('handles image load errors', () => {
    render(
      <ImageGallery
        images={mockImages}
        selectedIds={[]}
        {...mockHandlers}
      />
    );

    const firstImage = screen.getAllByRole('img')[0];
    fireEvent.error(firstImage);

    expect(screen.getByText(/failed to load/i)).toBeInTheDocument();
  });
});