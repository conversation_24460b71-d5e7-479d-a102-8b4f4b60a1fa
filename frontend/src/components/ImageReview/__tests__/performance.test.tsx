import React from 'react';
import { render } from '@testing-library/react';
import { ImageGallery } from '../ImageGallery';
import { ImageComparison } from '../ImageComparison';
import { ScoreDisplay } from '../ScoreDisplay';

// Extend global namespace for performance.memory
declare global {
  interface Performance {
    memory?: {
      usedJSHeapSize: number;
      totalJSHeapSize: number;
    };
  }
  namespace JSX {
    interface Element {}
  }
}

describe('Image Review Components Performance', () => {
  // Generate large dataset for testing
  const generateMockImages = (count: number) => {
    return Array.from({ length: count }, (_, i) => ({
      id: `img-${i}`,
      url: `https://example.com/image-${i}.jpg`,
      score: Math.random(),
      selected: false
    }));
  };

  const generateScoreHistory = (count: number) => {
    return Array.from({ length: count }, (_, i) => ({
      timestamp: new Date(Date.now() - i * 86400000).toISOString(),
      overall: Math.random()
    }));
  };

  // Memory usage monitoring
  const getMemoryUsage = () => {
    if (window.performance && window.performance.memory) {
      return {
        usedJSHeapSize: window.performance.memory.usedJSHeapSize,
        totalJSHeapSize: window.performance.memory.totalJSHeapSize
      };
    }
    return null;
  };

  // Performance measurement
  const measureRenderTime = (component: JSX.Element) => {
    const start = performance.now();
    render(component);
    return performance.now() - start;
  };

  describe('ImageGallery Performance', () => {
    it('handles large datasets efficiently', () => {
      const largeImageSet = generateMockImages(1000);
      const beforeMemory = getMemoryUsage();

      const renderTime = measureRenderTime(
        <ImageGallery
          images={largeImageSet}
          selectedIds={[]}
          onSelect={() => {}}
          onApprove={() => {}}
          onReject={() => {}}
        />
      );

      const afterMemory = getMemoryUsage();

      // Log performance metrics
      console.log('ImageGallery Render Time:', renderTime, 'ms');
      if (beforeMemory && afterMemory) {
        console.log('Memory Usage Increase:',
          (afterMemory.usedJSHeapSize - beforeMemory.usedJSHeapSize) / 1024 / 1024,
          'MB'
        );
      }

      // Performance assertions
      expect(renderTime).toBeLessThan(1000); // Should render in less than 1 second
    });

    it('maintains smooth scrolling with virtual list', () => {
      const scrollableImageSet = generateMockImages(5000);
      const { container } = render(
        <ImageGallery
          images={scrollableImageSet}
          selectedIds={[]}
          onSelect={() => {}}
          onApprove={() => {}}
          onReject={() => {}}
        />
      );

      // Measure scroll performance
      const startScroll = performance.now();
      container.scrollTop = 1000;
      const scrollTime = performance.now() - startScroll;

      expect(scrollTime).toBeLessThan(16.67); // Target 60fps (16.67ms per frame)
    });
  });

  describe('ImageComparison Performance', () => {
    it('handles zoom and pan operations efficiently', () => {
      const { container } = render(
        <ImageComparison
          beforeImage="large-image-before.jpg"
          afterImage="large-image-after.jpg"
          beforeScore={0.8}
          afterScore={0.9}
        />
      );

      const startZoom = performance.now();
      // Simulate zoom operation
      container.dispatchEvent(
        new WheelEvent('wheel', { deltaY: -100, ctrlKey: true })
      );
      const zoomTime = performance.now() - startZoom;

      expect(zoomTime).toBeLessThan(16.67); // Should be smooth at 60fps
    });
  });

  describe('ScoreDisplay Performance', () => {
    it('renders large history dataset efficiently', () => {
      const largeHistory = generateScoreHistory(1000);
      const scores = {
        overall: 0.85,
        quality: 0.9,
        composition: 0.8,
        lighting: 0.85,
        background: 0.75
      };

      const renderTime = measureRenderTime(
        <ScoreDisplay scores={scores} history={largeHistory} />
      );

      expect(renderTime).toBeLessThan(500); // Should render in less than 500ms
    });

    it('updates scores without performance degradation', () => {
      const history = generateScoreHistory(100);
      const { rerender } = render(
        <ScoreDisplay
          scores={{
            overall: 0.85,
            quality: 0.9,
            composition: 0.8,
            lighting: 0.85,
            background: 0.75
          }}
          history={history}
        />
      );

      const updateTimes = [];
      for (let i = 0; i < 10; i++) {
        const start = performance.now();
        rerender(
          <ScoreDisplay
            scores={{
              overall: Math.random(),
              quality: Math.random(),
              composition: Math.random(),
              lighting: Math.random(),
              background: Math.random()
            }}
            history={history}
          />
        );
        updateTimes.push(performance.now() - start);
      }

      const averageUpdateTime = updateTimes.reduce((a, b) => a + b) / updateTimes.length;
      expect(averageUpdateTime).toBeLessThan(16.67); // Should maintain 60fps
    });
  });
});