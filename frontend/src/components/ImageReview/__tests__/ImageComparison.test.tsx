import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import ImageComparison from '../ImageComparison';

describe('ImageComparison', () => {
  const mockProps = {
    beforeImage: 'before.jpg',
    afterImage: 'after.jpg',
    beforeScore: 0.75,
    afterScore: 0.95,
  };

  it('renders before and after images', () => {
    render(<ImageComparison {...mockProps} />);

    const beforeImg = screen.getByAltText('Before image');
    const afterImg = screen.getByAltText('After image');

    expect(beforeImg).toBeInTheDocument();
    expect(afterImg).toBeInTheDocument();
  });

  it('toggles between before/after view modes', () => {
    render(<ImageComparison {...mockProps} />);

    const toggleButton = screen.getByRole('button', { name: /toggle view/i });

    fireEvent.click(toggleButton);
    expect(screen.getByTestId('single-view')).toBeInTheDocument();

    fireEvent.click(toggleButton);
    expect(screen.getByTestId('side-by-side')).toBeInTheDocument();
  });

  it('synchronizes zoom and pan between images', () => {
    render(<ImageComparison {...mockProps} />);

    const beforeContainer = screen.getByTestId('before-container');
    const afterContainer = screen.getByTestId('after-container');

    fireEvent.wheel(beforeContainer, { deltaY: -100 });

    const beforeTransform = window.getComputedStyle(beforeContainer).transform;
    const afterTransform = window.getComputedStyle(afterContainer).transform;

    expect(beforeTransform).toBe(afterTransform);
  });

  it('displays score differences', () => {
    render(<ImageComparison {...mockProps} />);

    const scoreDiff = screen.getByTestId('score-difference');
    expect(scoreDiff).toHaveTextContent('20%'); // (0.95 - 0.75) * 100
  });

  it('handles image loading errors', () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

    render(
      <ImageComparison
        {...mockProps}
        beforeImage="invalid.jpg"
        afterImage="invalid.jpg"
      />
    );

    const errorMessage = screen.getByText(/failed to load image/i);
    expect(errorMessage).toBeInTheDocument();

    consoleSpy.mockRestore();
  });

  it('supports keyboard controls for zoom', () => {
    render(<ImageComparison {...mockProps} />);

    const container = screen.getByTestId('comparison-container');

    fireEvent.keyDown(container, { key: '+', ctrlKey: true });
    expect(container).toHaveStyle({ transform: 'scale(1.1)' });

    fireEvent.keyDown(container, { key: '-', ctrlKey: true });
    expect(container).toHaveStyle({ transform: 'scale(1)' });
  });
});