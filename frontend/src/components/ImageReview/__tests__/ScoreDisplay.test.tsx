import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ScoreDisplay } from '../ScoreDisplay';

describe('ScoreDisplay', () => {
  const mockScores = {
    overall: 0.85,
    quality: 0.9,
    composition: 0.8,
    lighting: 0.85,
    background: 0.75
  };

  const mockHistory = [
    { timestamp: '2024-01-01T10:00:00Z', overall: 0.8 },
    { timestamp: '2024-01-01T11:00:00Z', overall: 0.85 }
  ];

  it('renders all score indicators', () => {
    render(<ScoreDisplay scores={mockScores} history={mockHistory} />);

    expect(screen.getByTestId('overall-score')).toHaveTextContent('85%');
    expect(screen.getByTestId('quality-score')).toHaveTextContent('90%');
    expect(screen.getByTestId('composition-score')).toHaveTextContent('80%');
    expect(screen.getByTestId('lighting-score')).toHaveTextContent('85%');
    expect(screen.getByTestId('background-score')).toHaveTextContent('75%');
  });

  it('displays color-coded quality levels', () => {
    render(<ScoreDisplay scores={mockScores} history={mockHistory} />);

    const qualityIndicator = screen.getByTestId('quality-indicator');

    // Test for green color on high scores (>= 0.8)
    expect(qualityIndicator).toHaveClass('bg-green-500');

    // Render with lower scores and test for different colors
    render(
      <ScoreDisplay
        scores={{ ...mockScores, quality: 0.6 }}
        history={mockHistory}
      />
    );

    const warningIndicator = screen.getByTestId('quality-indicator');
    expect(warningIndicator).toHaveClass('bg-yellow-500');
  });

  it('shows tooltips with detailed information', () => {
    render(<ScoreDisplay scores={mockScores} history={mockHistory} />);

    const qualityScore = screen.getByTestId('quality-score');
    fireEvent.mouseEnter(qualityScore);

    expect(screen.getByRole('tooltip')).toBeInTheDocument();
    expect(screen.getByRole('tooltip')).toHaveTextContent(
      /Image quality assessment/i
    );
  });

  it('displays score history in a chart', () => {
    render(<ScoreDisplay scores={mockScores} history={mockHistory} />);

    const historyChart = screen.getByTestId('score-history-chart');
    expect(historyChart).toBeInTheDocument();

    // Verify history points are plotted
    const dataPoints = screen.getAllByTestId('history-point');
    expect(dataPoints).toHaveLength(mockHistory.length);
  });

  it('handles empty history gracefully', () => {
    render(<ScoreDisplay scores={mockScores} history={[]} />);

    const historyChart = screen.getByTestId('score-history-chart');
    expect(historyChart).toHaveTextContent(/no history available/i);
  });

  it('updates when scores change', () => {
    const { rerender } = render(
      <ScoreDisplay scores={mockScores} history={mockHistory} />
    );

    const newScores = { ...mockScores, overall: 0.9 };
    rerender(<ScoreDisplay scores={newScores} history={mockHistory} />);

    expect(screen.getByTestId('overall-score')).toHaveTextContent('90%');
  });

  it('handles invalid score values', () => {
    const invalidScores = {
      ...mockScores,
      quality: 1.2, // Invalid score > 1
      composition: -0.1 // Invalid score < 0
    };

    render(<ScoreDisplay scores={invalidScores} history={mockHistory} />);

    expect(screen.getByTestId('quality-score')).toHaveTextContent('100%');
    expect(screen.getByTestId('composition-score')).toHaveTextContent('0%');
  });
});