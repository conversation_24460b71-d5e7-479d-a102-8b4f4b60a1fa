import { useAppStore } from '../../store';
import { useImageReview } from '../../hooks/useImageReview';

type ImageCardProps = {
  url: string;
  quality: number;
  status: 'pending' | 'accepted' | 'rejected';
  onAccept: () => void;
  onReject: () => void;
};

function ImageCard({ url, quality, status, onAccept, onReject }: ImageCardProps) {
  return (
    <div className="bg-card rounded-lg overflow-hidden shadow-sm">
      <div className="aspect-square relative">
        <img
          src={url}
          alt="Product"
          className="w-full h-full object-cover"
          loading="lazy"
        />
        <div className="absolute top-2 right-2 px-2 py-1 bg-background/80 backdrop-blur-sm rounded text-sm">
          {Math.round(quality * 100)}%
        </div>
      </div>

      <div className="p-4 space-y-4">
        <div className="flex items-center justify-between gap-2">
          {status === 'pending' ? (
            <>
              <button
                onClick={onAccept}
                className="flex-1 px-3 py-1.5 bg-primary text-primary-foreground text-sm rounded-md hover:bg-primary/90"
              >
                Accept
              </button>
              <button
                onClick={onReject}
                className="flex-1 px-3 py-1.5 bg-destructive text-destructive-foreground text-sm rounded-md hover:bg-destructive/90"
              >
                Reject
              </button>
            </>
          ) : (
            <div
              className={`w-full text-center py-1.5 text-sm rounded-md ${
                status === 'accepted'
                  ? 'bg-primary/10 text-primary'
                  : 'bg-destructive/10 text-destructive'
              }`}
            >
              {status === 'accepted' ? 'Accepted' : 'Rejected'}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export function ImageReview() {
  const selectedProductId = useAppStore((state) => state.selectedProductId);
  const setSelectedProductId = useAppStore((state) => state.setSelectedProductId);
  const { product, acceptImage, rejectImage } = useImageReview(selectedProductId);

  if (!product) {
    return null;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">
          Images for {product.name || product.id}
        </h2>
        <button
          onClick={() => setSelectedProductId(null)}
          className="text-sm text-muted-foreground hover:text-foreground"
        >
          ← Back to Products
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {product.images.map((image) => (
          <ImageCard
            key={image.url}
            url={image.url}
            quality={image.quality}
            status={image.status}
            onAccept={() => acceptImage(image.url)}
            onReject={() => rejectImage(image.url)}
          />
        ))}
      </div>
    </div>
  );
}