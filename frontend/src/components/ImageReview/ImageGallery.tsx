import { useCallback, useEffect, useRef, useState } from 'react';
import { useVirtualizer } from '@tanstack/react-virtual';

interface Image {
  id: string;
  url: string;
  score: number;
  status: 'approved' | 'rejected' | 'pending';
  metadata?: Record<string, unknown>;
}

interface ImageGalleryProps {
  images: Image[];
  selectedImageId?: string;
  onImageSelect: (imageId: string) => void;
  onImageAction?: (imageId: string, action: 'approve' | 'reject') => void;
}

export function ImageGallery({
  images,
  selectedImageId,
  onImageSelect,
  onImageAction
}: ImageGalleryProps) {
  const parentRef = useRef<HTMLDivElement>(null);
  const [selectedIds, setSelectedIds] = useState<Set<string>>(new Set());

  // Calculate the number of columns based on container width
  const [columns, setColumns] = useState(3);
  const updateColumns = useCallback(() => {
    if (!parentRef.current) return;
    const width = parentRef.current.offsetWidth;
    const newColumns = Math.max(1, Math.floor(width / 300)); // 300px minimum width per item
    setColumns(newColumns);
  }, []);

  useEffect(() => {
    updateColumns();
    const observer = new ResizeObserver(updateColumns);
    if (parentRef.current) {
      observer.observe(parentRef.current);
    }
    return () => observer.disconnect();
  }, [updateColumns]);

  // Create rows for virtual list
  const rows = Math.ceil(images.length / columns);
  const getItemsForRow = (rowIndex: number) => {
    const startIndex = rowIndex * columns;
    return images.slice(startIndex, startIndex + columns);
  };

  const rowVirtualizer = useVirtualizer({
    count: rows,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 300, // Fixed row height
    overscan: 5 // Number of items to render outside of the visible area
  });

  const handleImageClick = (imageId: string, event: React.MouseEvent) => {
    if (event.ctrlKey || event.metaKey) {
      // Multi-select with Ctrl/Cmd key
      const newSelectedIds = new Set(selectedIds);
      if (newSelectedIds.has(imageId)) {
        newSelectedIds.delete(imageId);
      } else {
        newSelectedIds.add(imageId);
      }
      setSelectedIds(newSelectedIds);
    } else if (event.shiftKey && selectedIds.size > 0) {
      // Range select with Shift key
      const lastSelectedId = Array.from(selectedIds)[selectedIds.size - 1];
      const lastIndex = images.findIndex(img => img.id === lastSelectedId);
      const currentIndex = images.findIndex(img => img.id === imageId);
      const start = Math.min(lastIndex, currentIndex);
      const end = Math.max(lastIndex, currentIndex);
      const newSelectedIds = new Set(
        images.slice(start, end + 1).map(img => img.id)
      );
      setSelectedIds(newSelectedIds);
    } else {
      // Single select
      setSelectedIds(new Set([imageId]));
      onImageSelect(imageId);
    }
  };

  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      if (!selectedImageId) return;

      const currentIndex = images.findIndex(img => img.id === selectedImageId);
      if (currentIndex === -1) return;

      let newIndex: number | null = null;

      switch (event.key) {
        case 'ArrowLeft':
          newIndex = Math.max(0, currentIndex - 1);
          break;
        case 'ArrowRight':
          newIndex = Math.min(images.length - 1, currentIndex + 1);
          break;
        case 'ArrowUp':
          newIndex = Math.max(0, currentIndex - columns);
          break;
        case 'ArrowDown':
          newIndex = Math.min(images.length - 1, currentIndex + columns);
          break;
        case 'Enter':
          if (onImageAction && selectedImageId) {
            onImageAction(selectedImageId, 'approve');
          }
          break;
        case 'Backspace':
        case 'Delete':
          if (onImageAction && selectedImageId) {
            onImageAction(selectedImageId, 'reject');
          }
          break;
        default:
          return;
      }

      if (newIndex !== null) {
        event.preventDefault();
        const newImageId = images[newIndex].id;
        onImageSelect(newImageId);
        setSelectedIds(new Set([newImageId]));

        // Ensure the new selection is in view
        const newRowIndex = Math.floor(newIndex / columns);
        rowVirtualizer.scrollToIndex(newRowIndex);
      }
    },
    [selectedImageId, images, columns, onImageSelect, onImageAction, rowVirtualizer]
  );

  useEffect(() => {
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyDown]);

  return (
    <div
      ref={parentRef}
      className="w-full h-[calc(100vh-200px)] overflow-auto"
      tabIndex={0} // Make the container focusable for keyboard events
    >
      <div
        style={{
          height: `${rowVirtualizer.getTotalSize()}px`,
          width: '100%',
          position: 'relative'
        }}
      >
        {rowVirtualizer.getVirtualItems().map(virtualRow => {
          const items = getItemsForRow(virtualRow.index);
          return (
            <div
              key={virtualRow.index}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: `${virtualRow.size}px`,
                transform: `translateY(${virtualRow.start}px)`
              }}
              className="grid gap-4"
              style={{
                gridTemplateColumns: `repeat(${columns}, minmax(0, 1fr))`
              }}
            >
              {items.map(image => (
                <div
                  key={image.id}
                  className={`relative aspect-square rounded-lg overflow-hidden border-2 transition-colors cursor-pointer ${selectedIds.has(image.id) ? 'border-primary' : 'border-transparent hover:border-primary/50'}`}
                  onClick={e => handleImageClick(image.id, e)}
                >
                  <img
                    src={image.url}
                    alt=""
                    className="w-full h-full object-cover"
                    loading="lazy"
                  />
                  <div className="absolute bottom-0 left-0 right-0 p-2 bg-gradient-to-t from-black/50 to-transparent text-white">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">
                        Score: {Math.round(image.score)}
                      </span>
                      <span
                        className={`px-2 py-0.5 rounded-full text-xs ${getStatusColor(image.status)}`}
                      >
                        {image.status}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          );
        })}
      </div>
    </div>
  );
}

function getStatusColor(status: string) {
  switch (status) {
    case 'approved':
      return 'bg-green-500';
    case 'rejected':
      return 'bg-red-500';
    default:
      return 'bg-yellow-500';
  }
}