import { Info } from 'lucide-react';

interface Score {
  value: number;
  label: string;
  description?: string;
}

interface ScoreDisplayProps {
  scores: Score[];
  overallScore: number;
  history?: {
    timestamp: string;
    score: number;
  }[];
}

export function ScoreDisplay({ scores, overallScore, history }: ScoreDisplayProps) {
  const getScoreColor = (score: number) => {
    if (score >= 90) return 'bg-green-500';
    if (score >= 70) return 'bg-yellow-500';
    if (score >= 50) return 'bg-orange-500';
    return 'bg-red-500';
  };

  const formatDate = (timestamp: string) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric'
    });
  };

  return (
    <div className="space-y-6 p-4 bg-card rounded-lg border">
      <div className="space-y-2">
        <h3 className="text-lg font-semibold">Quality Score</h3>
        <div className="flex items-center gap-2">
          <div
            className={`text-3xl font-bold px-3 py-1 rounded ${getScoreColor(
              overallScore
            )} text-white`}
          >
            {Math.round(overallScore)}
          </div>
          <span className="text-sm text-muted-foreground">Overall Score</span>
        </div>
      </div>

      <div className="space-y-3">
        {scores.map((score, index) => (
          <div key={index} className="space-y-1">
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-1">
                {score.label}
                {score.description && (
                  <div className="group relative">
                    <Info className="w-4 h-4 text-muted-foreground cursor-help" />
                    <div className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 px-3 py-1 bg-popover text-popover-foreground text-xs rounded shadow-lg opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap">
                      {score.description}
                    </div>
                  </div>
                )}
              </div>
              <span className="font-medium">{Math.round(score.value)}</span>
            </div>
            <div className="h-2 bg-muted rounded-full overflow-hidden">
              <div
                className={`h-full ${getScoreColor(score.value)} transition-all`}
                style={{ width: `${score.value}%` }}
              />
            </div>
          </div>
        ))}
      </div>

      {history && history.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Score History</h4>
          <div className="space-y-1">
            {history.map((entry, index) => (
              <div
                key={index}
                className="flex items-center justify-between text-sm py-1 border-b last:border-0"
              >
                <span className="text-muted-foreground">
                  {formatDate(entry.timestamp)}
                </span>
                <span
                  className={`px-2 py-0.5 rounded-full text-xs font-medium ${getScoreColor(
                    entry.score
                  )} text-white`}
                >
                  {Math.round(entry.score)}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}