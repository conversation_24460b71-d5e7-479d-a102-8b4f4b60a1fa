import { QueryClient } from '@tanstack/react-query';

export const API_BASE_URL = 'http://localhost:8000';
export const WS_BASE_URL = 'ws://localhost:8000';

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

interface ApiError {
  message: string;
  code?: string;
}

export class ApiService {
  private static async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const error: ApiError = await response.json().catch(() => ({
        message: 'An unexpected error occurred',
      }));
      throw new Error(error.message);
    }
    return response.json();
  }

  static async uploadProducts(file: File) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('search_front', 'true');
    formData.append('search_back', 'true');

    const response = await fetch(`${API_BASE_URL}/api/products/upload/csv`, {
      method: 'POST',
      body: formData,
    });

    return this.handleResponse<{ job_id: string }>(response);
  }

  static async getJobStatus(jobId: string) {
    const response = await fetch(`${API_BASE_URL}/api/products/jobs/${jobId}`);
    return this.handleResponse<{
      status: 'pending' | 'processing' | 'completed' | 'failed';
      progress: number;
      message?: string;
    }>(response);
  }

  static async getJobResults(jobId: string) {
    const response = await fetch(`${API_BASE_URL}/api/products/jobs/${jobId}/results`);
    return this.handleResponse<{
      products: Array<{
        id: string;
        name: string;
        images: Array<{
          url: string;
          score: number;
        }>;
      }>;
    }>(response);
  }

  static async exportResults(jobId: string, format: 'json' | 'csv'): Promise<string | void> {
    const response = await fetch(
      `${API_BASE_URL}/api/products/jobs/${jobId}/export?format=${format}`,
      {
        headers: {
          Accept: format === 'json' ? 'application/json' : 'text/csv',
        },
      }
    );

    if (format === 'csv') {
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `results-${jobId}.csv`;
      a.click();
      window.URL.revokeObjectURL(url);
      return;
    }

    return this.handleResponse<string>(response);
  }
}