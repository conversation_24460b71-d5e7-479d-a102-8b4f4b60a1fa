"""Utility functions for the product image automation system."""

import json
import csv
import time
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional

from models import ProductImageResult, BatchProcessingResult, ImageAssessment


def save_results_to_json(
    results: BatchProcessingResult,
    output_path: str,
    include_metadata: bool = True
) -> str:
    """Save batch processing results to JSON file.

    Args:
        results: BatchProcessingResult to save
        output_path: Path to save the JSON file
        include_metadata: Whether to include processing metadata

    Returns:
        Path to the saved file
    """
    output_path = Path(output_path)
    output_path.parent.mkdir(parents=True, exist_ok=True)

    # Convert results to dictionary
    data = {
        "summary": {
            "total_products": results.total_products,
            "successful_products": results.successful_products,
            "failed_products": results.failed_products,
            "success_rate": results.successful_products / results.total_products if results.total_products > 0 else 0,
            "total_processing_time": results.total_processing_time,
            "average_time_per_product": results.total_processing_time / results.total_products if results.total_products > 0 else 0
        },
        "products": []
    }

    if include_metadata:
        data["metadata"] = {
            "generated_at": datetime.now().isoformat(),
            "version": "1.0",
            "system": "ProductImageAutomation"
        }

    # Process each product result
    for result in results.products:
        product_data = {
            "product_name": result.product_name,
            "success": result.success,
            "processing_time": result.processing_time,
            "front_view_images": [],
        "back_view_images": []
        }

        if result.error_message:
            product_data["error_message"] = result.error_message

        # Add front view images
        for assessment in result.all_front_assessments:
            image_data = {
                "image_url": str(assessment.image_url),
                "quality_score": assessment.quality_score,
                "overall_score": assessment.overall_score,
                "product_match": assessment.product_match,
                "correct_view": assessment.correct_view,
                "reasoning": assessment.reasoning
            }

            if assessment.local_path:
                image_data["local_path"] = assessment.local_path

            if assessment.has_nutrition_info is not None:
                image_data["has_nutrition_info"] = assessment.has_nutrition_info

            if assessment.error_message:
                image_data["error_message"] = assessment.error_message

            product_data["front_view_images"].append(image_data)

        # Add back view images
        for assessment in result.all_back_assessments:
            image_data = {
                "image_url": str(assessment.image_url),
                "quality_score": assessment.quality_score,
                "overall_score": assessment.overall_score,
                "product_match": assessment.product_match,
                "correct_view": assessment.correct_view,
                "reasoning": assessment.reasoning
            }

            if assessment.local_path:
                image_data["local_path"] = assessment.local_path

            if assessment.has_nutrition_info is not None:
                image_data["has_nutrition_info"] = assessment.has_nutrition_info

            if assessment.error_message:
                image_data["error_message"] = assessment.error_message

            product_data["back_view_images"].append(image_data)

        data["products"].append(product_data)

    # Save to file
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)

    return str(output_path)


def save_results_to_csv(
    results: BatchProcessingResult,
    output_path: str,
    flatten_images: bool = True
) -> str:
    """Save batch processing results to CSV file.

    Args:
        results: BatchProcessingResult to save
        output_path: Path to save the CSV file
        flatten_images: Whether to create separate rows for each image

    Returns:
        Path to the saved file
    """
    output_path = Path(output_path)
    output_path.parent.mkdir(parents=True, exist_ok=True)

    if flatten_images:
        # Create one row per image
        fieldnames = [
            'product_name', 'success', 'processing_time', 'error_message',
            'view_type', 'image_url', 'local_path', 'quality_score',
            'overall_score', 'product_match', 'correct_view',
            'has_nutrition_info', 'reasoning', 'image_error_message'
        ]

        with open(output_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()

            for result in results.products:
                base_row = {
                    'product_name': result.product_name,
                    'success': result.success,
                    'processing_time': result.processing_time,
                    'error_message': result.error_message or ''
                }

                # Add front view images
                for assessment in result.all_front_assessments:
                    row = base_row.copy()
                    row.update({
                        'view_type': 'front',
                        'image_url': str(assessment.image_url),
                        'local_path': assessment.local_path or '',
                        'quality_score': assessment.quality_score,
                        'overall_score': assessment.overall_score,
                        'product_match': assessment.product_match,
                        'correct_view': assessment.correct_view,
                        'has_nutrition_info': assessment.has_nutrition_info if assessment.has_nutrition_info is not None else '',
                        'reasoning': assessment.reasoning,
                        'image_error_message': assessment.error_message or ''
                    })
                    writer.writerow(row)

                # Add back view images
                for assessment in result.all_back_assessments:
                    row = base_row.copy()
                    row.update({
                        'view_type': 'back',
                        'image_url': str(assessment.image_url),
                        'local_path': assessment.local_path or '',
                        'quality_score': assessment.quality_score,
                        'overall_score': assessment.overall_score,
                        'product_match': assessment.product_match,
                        'correct_view': assessment.correct_view,
                        'has_nutrition_info': assessment.has_nutrition_info if assessment.has_nutrition_info is not None else '',
                        'reasoning': assessment.reasoning,
                        'image_error_message': assessment.error_message or ''
                    })
                    writer.writerow(row)

                # If no images found, still add a row for the product
                if not result.all_front_assessments and not result.all_back_assessments:
                    row = base_row.copy()
                    row.update({key: '' for key in fieldnames if key not in base_row})
                    writer.writerow(row)

    else:
        # Create one row per product with summary information
        fieldnames = [
            'product_name', 'success', 'processing_time', 'error_message',
            'front_images_count', 'back_images_count', 'total_images_count',
            'avg_front_quality', 'avg_back_quality', 'has_nutrition_info'
        ]

        with open(output_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()

            for result in results.products:
                # Calculate averages
                front_quality_avg = (
                    sum(img.quality_score for img in result.all_front_assessments) / len(result.all_front_assessments)
                    if result.all_front_assessments else 0
                )

                back_quality_avg = (
                    sum(img.quality_score for img in result.all_back_assessments) / len(result.all_back_assessments)
                    if result.all_back_assessments else 0
                )

                # Check for nutrition info
                has_nutrition = any(
                    img.has_nutrition_info for img in result.all_back_assessments
                    if img.has_nutrition_info is not None
                )

                row = {
                    'product_name': result.product_name,
                    'success': result.success,
                    'processing_time': result.processing_time,
                    'error_message': result.error_message or '',
                    'front_images_count': len(result.all_front_assessments),
                    'back_images_count': len(result.all_back_assessments),
                    'total_images_count': len(result.all_front_assessments) + len(result.all_back_assessments),
                    'avg_front_quality': round(front_quality_avg, 2),
                    'avg_back_quality': round(back_quality_avg, 2),
                    'has_nutrition_info': has_nutrition
                }

                writer.writerow(row)

    return str(output_path)


def generate_summary_report(results: BatchProcessingResult) -> Dict[str, Any]:
    """Generate a comprehensive summary report.

    Args:
        results: BatchProcessingResult to analyze

    Returns:
        Dictionary containing summary statistics
    """
    if not results.products:
        return {
            "total_products": 0,
            "processing_summary": {},
            "image_summary": {},
            "quality_summary": {},
            "error_summary": {}
        }

    # Basic processing stats
    successful_results = [r for r in results.products if r.success]
    failed_results = [r for r in results.products if not r.success]

    # Image stats
    total_front_images = sum(len(r.all_front_assessments) for r in results.products)
    total_back_images = sum(len(r.all_back_assessments) for r in results.products)
    total_images = total_front_images + total_back_images

    # Quality stats
    all_assessments = []
    for result in results.products:
        all_assessments.extend(result.all_front_assessments)
        all_assessments.extend(result.all_back_assessments)

    quality_scores = [a.quality_score for a in all_assessments if a.quality_score > 0]
    overall_scores = [a.overall_score for a in all_assessments if a.overall_score > 0]

    # Nutrition info stats
    back_images_with_nutrition = [
        a for a in all_assessments
        if a.view_type.value == 'back' and a.has_nutrition_info is True
    ]

    # Error analysis
    error_types = {}
    for result in failed_results:
        if result.error_message:
            error_key = result.error_message.split(':')[0] if ':' in result.error_message else result.error_message
            error_types[error_key] = error_types.get(error_key, 0) + 1

    # Processing time stats
    processing_times = [r.processing_time for r in results.products if r.processing_time > 0]

    return {
        "total_products": results.total_products,
        "processing_summary": {
            "successful_products": results.successful_products,
            "failed_products": results.failed_products,
            "success_rate": results.successful_products / results.total_products if results.total_products > 0 else 0,
            "total_processing_time": results.total_processing_time,
            "average_time_per_product": results.total_processing_time / results.total_products if results.total_products > 0 else 0,
            "min_processing_time": min(processing_times) if processing_times else 0,
            "max_processing_time": max(processing_times) if processing_times else 0
        },
        "image_summary": {
            "total_images_found": total_images,
            "front_view_images": total_front_images,
            "back_view_images": total_back_images,
            "average_images_per_product": total_images / results.total_products if results.total_products > 0 else 0,
            "products_with_front_images": len([r for r in results.products if r.all_front_assessments]),
        "products_with_back_images": len([r for r in results.products if r.all_back_assessments]),
        "products_with_both_views": len([r for r in results.products if r.all_front_assessments and r.all_back_assessments])
        },
        "quality_summary": {
            "average_quality_score": sum(quality_scores) / len(quality_scores) if quality_scores else 0,
            "average_overall_score": sum(overall_scores) / len(overall_scores) if overall_scores else 0,
            "high_quality_images": len([s for s in quality_scores if s >= 8]),
            "medium_quality_images": len([s for s in quality_scores if 5 <= s < 8]),
            "low_quality_images": len([s for s in quality_scores if s < 5]),
            "images_with_nutrition_info": len(back_images_with_nutrition),
            "nutrition_info_rate": len(back_images_with_nutrition) / total_back_images if total_back_images > 0 else 0
        },
        "error_summary": {
            "total_errors": results.failed_products,
            "error_types": error_types,
            "most_common_error": max(error_types.items(), key=lambda x: x[1])[0] if error_types else None
        }
    }


def create_html_report(
    results: BatchProcessingResult,
    output_path: str,
    include_images: bool = True
) -> str:
    """Create an HTML report with visual results.

    Args:
        results: BatchProcessingResult to visualize
        output_path: Path to save the HTML file
        include_images: Whether to include image previews

    Returns:
        Path to the saved HTML file
    """
    output_path = Path(output_path)
    output_path.parent.mkdir(parents=True, exist_ok=True)

    summary = generate_summary_report(results)

    html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Image Automation Report</title>
    <style>
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .header {{
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }}
        .summary-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }}
        .summary-card {{
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }}
        .summary-card h3 {{
            margin: 0 0 15px 0;
            color: #333;
        }}
        .metric {{
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }}
        .metric-value {{
            font-weight: bold;
            color: #007bff;
        }}
        .product-grid {{
            display: grid;
            gap: 20px;
        }}
        .product-card {{
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            background: white;
        }}
        .product-card.success {{
            border-left: 4px solid #28a745;
        }}
        .product-card.failed {{
            border-left: 4px solid #dc3545;
        }}
        .product-header {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }}
        .product-name {{
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
        }}
        .status-badge {{
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
            text-transform: uppercase;
        }}
        .status-success {{
            background: #d4edda;
            color: #155724;
        }}
        .status-failed {{
            background: #f8d7da;
            color: #721c24;
        }}
        .images-section {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 15px;
        }}
        .view-section h4 {{
            margin: 0 0 10px 0;
            color: #666;
        }}
        .image-item {{
            border: 1px solid #eee;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 10px;
        }}
        .image-preview {{
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: 4px;
            margin-right: 10px;
            float: left;
        }}
        .image-details {{
            overflow: hidden;
        }}
        .score {{
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
            margin-right: 5px;
        }}
        .score-high {{ background: #d4edda; color: #155724; }}
        .score-medium {{ background: #fff3cd; color: #856404; }}
        .score-low {{ background: #f8d7da; color: #721c24; }}
        .error-message {{
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Product Image Automation Report</h1>
            <p>Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>

        <div class="summary-grid">
            <div class="summary-card">
                <h3>Processing Summary</h3>
                <div class="metric">
                    <span>Total Products:</span>
                    <span class="metric-value">{summary['total_products']}</span>
                </div>
                <div class="metric">
                    <span>Successful:</span>
                    <span class="metric-value">{summary['processing_summary']['successful_products']}</span>
                </div>
                <div class="metric">
                    <span>Failed:</span>
                    <span class="metric-value">{summary['processing_summary']['failed_products']}</span>
                </div>
                <div class="metric">
                    <span>Success Rate:</span>
                    <span class="metric-value">{summary['processing_summary']['success_rate']:.1%}</span>
                </div>
            </div>

            <div class="summary-card">
                <h3>Image Summary</h3>
                <div class="metric">
                    <span>Total Images:</span>
                    <span class="metric-value">{summary['image_summary']['total_images_found']}</span>
                </div>
                <div class="metric">
                    <span>Front View:</span>
                    <span class="metric-value">{summary['image_summary']['front_view_images']}</span>
                </div>
                <div class="metric">
                    <span>Back View:</span>
                    <span class="metric-value">{summary['image_summary']['back_view_images']}</span>
                </div>
                <div class="metric">
                    <span>Avg per Product:</span>
                    <span class="metric-value">{summary['image_summary']['average_images_per_product']:.1f}</span>
                </div>
            </div>

            <div class="summary-card">
                <h3>Quality Summary</h3>
                <div class="metric">
                    <span>Avg Quality Score:</span>
                    <span class="metric-value">{summary['quality_summary']['average_quality_score']:.1f}/10</span>
                </div>
                <div class="metric">
                    <span>High Quality Images:</span>
                    <span class="metric-value">{summary['quality_summary']['high_quality_images']}</span>
                </div>
                <div class="metric">
                    <span>With Nutrition Info:</span>
                    <span class="metric-value">{summary['quality_summary']['images_with_nutrition_info']}</span>
                </div>
                <div class="metric">
                    <span>Nutrition Rate:</span>
                    <span class="metric-value">{summary['quality_summary']['nutrition_info_rate']:.1%}</span>
                </div>
            </div>

            <div class="summary-card">
                <h3>Performance</h3>
                <div class="metric">
                    <span>Total Time:</span>
                    <span class="metric-value">{summary['processing_summary']['total_processing_time']:.1f}s</span>
                </div>
                <div class="metric">
                    <span>Avg per Product:</span>
                    <span class="metric-value">{summary['processing_summary']['average_time_per_product']:.1f}s</span>
                </div>
                <div class="metric">
                    <span>Min Time:</span>
                    <span class="metric-value">{summary['processing_summary']['min_processing_time']:.1f}s</span>
                </div>
                <div class="metric">
                    <span>Max Time:</span>
                    <span class="metric-value">{summary['processing_summary']['max_processing_time']:.1f}s</span>
                </div>
            </div>
        </div>

        <h2>Product Details</h2>
        <div class="product-grid">
"""

    # Add product details
    for result in results.products:
        status_class = "success" if result.success else "failed"
        status_badge_class = "status-success" if result.success else "status-failed"
        status_text = "Success" if result.success else "Failed"

        html_content += f"""
            <div class="product-card {status_class}">
                <div class="product-header">
                    <div class="product-name">{result.product_name}</div>
                    <div class="status-badge {status_badge_class}">{status_text}</div>
                </div>
                <div class="metric">
                    <span>Processing Time:</span>
                    <span class="metric-value">{result.processing_time:.2f}s</span>
                </div>
        """

        if result.error_message:
            html_content += f'<div class="error-message">Error: {result.error_message}</div>'

        if result.all_front_assessments or result.all_back_assessments:
            html_content += '<div class="images-section">'

            # Front view images
            html_content += '<div class="view-section">'
            html_content += f'<h4>Front View Images ({len(result.all_front_assessments)})</h4>'

            for assessment in result.all_front_assessments:
                score_class = "score-high" if assessment.overall_score >= 8 else "score-medium" if assessment.overall_score >= 5 else "score-low"

                html_content += f"""
                <div class="image-item">
                    {f'<img src="{str(assessment.image_url)}" class="image-preview" alt="Product image" onerror="this.style.display=\'none\'">' if include_images else ''}
                    <div class="image-details">
                        <div>
                            <span class="score {score_class}">Quality: {assessment.quality_score}/10</span>
                            <span class="score {score_class}">Overall: {assessment.overall_score}/10</span>
                        </div>
                        <div style="font-size: 0.9em; color: #666; margin-top: 5px;">
                            {assessment.reasoning}
                        </div>
                        {f'<div style="font-size: 0.8em; color: #999; margin-top: 5px;">Local: {assessment.local_path}</div>' if assessment.local_path else ''}
                    </div>
                </div>
                """

            html_content += '</div>'

            # Back view images
            html_content += '<div class="view-section">'
            html_content += f'<h4>Back View Images ({len(result.all_back_assessments)})</h4>'

            for assessment in result.all_back_assessments:
                score_class = "score-high" if assessment.overall_score >= 8 else "score-medium" if assessment.overall_score >= 5 else "score-low"
                nutrition_badge = '<span class="score score-high">Nutrition ✓</span>' if assessment.has_nutrition_info else ''

                html_content += f"""
                <div class="image-item">
                    {f'<img src="{str(assessment.image_url)}" class="image-preview" alt="Product image" onerror="this.style.display=\'none\'">' if include_images else ''}
                    <div class="image-details">
                        <div>
                            <span class="score {score_class}">Quality: {assessment.quality_score}/10</span>
                            <span class="score {score_class}">Overall: {assessment.overall_score}/10</span>
                            {nutrition_badge}
                        </div>
                        <div style="font-size: 0.9em; color: #666; margin-top: 5px;">
                            {assessment.reasoning}
                        </div>
                        {f'<div style="font-size: 0.8em; color: #999; margin-top: 5px;">Local: {assessment.local_path}</div>' if assessment.local_path else ''}
                    </div>
                </div>
                """

            html_content += '</div></div>'

        html_content += '</div>'

    html_content += """
        </div>
    </div>
</body>
</html>
"""

    # Save HTML file
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(html_content)

    return str(output_path)


def load_product_list(file_path: str) -> List[str]:
    """Load product list from various file formats.

    Args:
        file_path: Path to the file containing product names

    Returns:
        List of product names
    """
    file_path = Path(file_path)

    if not file_path.exists():
        raise FileNotFoundError(f"Product list file not found: {file_path}")

    products = []

    if file_path.suffix.lower() == '.json':
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            if isinstance(data, list):
                products = [str(item) for item in data]
            elif isinstance(data, dict) and 'products' in data:
                products = [str(item) for item in data['products']]
            else:
                raise ValueError("JSON file must contain a list or have a 'products' key")

    elif file_path.suffix.lower() == '.csv':
        with open(file_path, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            # Skip header if it exists
            first_row = next(reader, None)
            if first_row and first_row[0].lower() in ['product', 'product_name', 'name']:
                # Has header, continue reading
                pass
            else:
                # No header, use first row
                if first_row:
                    products.append(first_row[0].strip())

            # Read remaining rows
            for row in reader:
                if row and row[0].strip():
                    products.append(row[0].strip())

    else:
        # Treat as plain text file (one product per line)
        with open(file_path, 'r', encoding='utf-8') as f:
            products = [line.strip() for line in f if line.strip()]

    # Remove duplicates while preserving order
    seen = set()
    unique_products = []
    for product in products:
        if product not in seen:
            seen.add(product)
            unique_products.append(product)

    return unique_products