from fastapi import APIRouter, HTTPException, BackgroundTasks, UploadFile, File, Form
from fastapi.responses import FileResponse, JSONResponse
from typing import List, Dict, Any
import csv
import io
import os
import json
from datetime import datetime
from pathlib import Path

from app.core.automation import AutomationWrapper
from app.models.api import (
    ProductProcessRequest, JobResponse, JobStatusResponse,
    ErrorResponse, FileUploadResponse, StorageInfoResponse
)
from app.utils.file_validator import validate_csv_file
from app.core.config import get_settings

settings = get_settings()

router = APIRouter(prefix="/api/products", tags=["products"])
automation = AutomationWrapper()

@router.post("/upload/csv", response_model=FileUploadResponse)
async def upload_product_csv(
    file: UploadFile = File(...),
    search_front: bool = Form(True),
    search_back: bool = Form(True)
) -> FileUploadResponse:
    """Upload a CSV file containing product names for processing."""
    try:
        # Validate file type
        if not file.filename.endswith('.csv'):
            raise HTTPException(
                status_code=400,
                detail="Only CSV files are allowed"
            )

        # Read and validate CSV content
        content = await file.read()
        product_names, validation_errors = validate_csv_file(content)

        # Check for validation errors
        if validation_errors:
            raise HTTPException(
                status_code=400,
                detail=" | ".join(validation_errors)
            )

        # Create processing job
        job_id = await automation.create_job(
            product_names=product_names,
            search_front=search_front,
            search_back=search_back
        )

        return FileUploadResponse(
            job_id=job_id,
            filename=file.filename,
            product_count=len(product_names)
        )

    except UnicodeDecodeError:
        raise HTTPException(
            status_code=400,
            detail="Invalid CSV file encoding. Please use UTF-8 encoding."
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.post("/process", response_model=JobResponse)
async def process_products(request: ProductProcessRequest) -> JobResponse:
    """Start processing a list of products."""
    try:
        job_id = await automation.create_job(
            product_names=request.product_names,
            search_front=request.search_front,
            search_back=request.search_back
        )

        job_data = automation.get_job_status(job_id)
        if not job_data:
            raise HTTPException(status_code=500, detail="Failed to create job")

        return JobResponse(
            job_id=job_id,
            status=job_data["status"],
            created_at=job_data["created_at"],
            total_products=job_data["total_products"]
        )

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.get("/jobs/{job_id}", response_model=JobStatusResponse)
async def get_job_status(job_id: str) -> JobStatusResponse:
    """Get status of a processing job."""
    job_data = automation.get_job_status(job_id)
    if not job_data:
        raise HTTPException(
            status_code=404,
            detail=f"Job {job_id} not found"
        )

    # Add job_id to the response data since it's required by JobStatusResponse
    response_data = {**job_data, "job_id": job_id}
    return JobStatusResponse(**response_data)

@router.get("/jobs/{job_id}/results")
async def get_job_results(job_id: str):
    """Get results of a completed processing job."""
    job_data = automation.get_job_status(job_id)
    if not job_data:
        raise HTTPException(
            status_code=404,
            detail=f"Job {job_id} not found"
        )

    if job_data["status"] != "completed":
        raise HTTPException(
            status_code=400,
            detail=f"Job {job_id} is not completed yet. Current status: {job_data['status']}"
        )

    # Get results from job data
    results = job_data.get("results", {})

    # Transform results to match frontend expectations
    products = []
    for product_name, product_data in results.items():
        products.append({
            "id": product_name,
            "name": product_name,
            "images": [
                {
                    "url": f"/api/products/images/{job_id}/{product_name}/front",
                    "score": product_data.get("front_score", 0.0)
                } if product_data.get("front_image") else None,
                {
                    "url": f"/api/products/images/{job_id}/{product_name}/back",
                    "score": product_data.get("back_score", 0.0)
                } if product_data.get("back_image") else None
            ]
        })

    return {"products": [p for p in products if p]}

@router.post("/jobs/{job_id}/cancel")
async def cancel_job(job_id: str):
    """Cancel a running or queued job."""
    success = automation.cancel_job(job_id)
    if not success:
        raise HTTPException(
            status_code=400,
            detail=f"Could not cancel job {job_id}. Job may be completed or not found."
        )
    return {"status": "cancelled", "job_id": job_id}

@router.get("/images/{job_id}/{product_name}/{image_type}", response_class=FileResponse)
async def get_product_image(job_id: str, product_name: str, image_type: str):
    """Serve a processed product image.

    Args:
        job_id (str): ID of the processing job
        product_name (str): Name of the product
        image_type (str): Type of image (front/back)
    """
    # Validate image type
    if image_type not in ['front', 'back']:
        raise HTTPException(
            status_code=400,
            detail="Invalid image type. Must be 'front' or 'back'"
        )

    # Construct image path
    image_dir = Path(settings.IMAGES_DIR) / job_id / product_name
    image_path = image_dir / f"{image_type}.jpg"

    # Check if image exists
    if not image_path.exists():
        raise HTTPException(
            status_code=404,
            detail=f"Image not found for product {product_name}"
        )

    return FileResponse(
        str(image_path),
        media_type="image/jpeg",
        filename=f"{product_name}_{image_type}.jpg"
    )

@router.get("/jobs/{job_id}/export", response_class=FileResponse)
async def export_job_results(job_id: str, format: str = "json"):
    """Export job results in the specified format.

    Args:
        job_id (str): ID of the processing job
        format (str): Export format (json/csv)
    """
    # Get job status and validate
    job_data = automation.get_job_status(job_id)
    if not job_data:
        raise HTTPException(
            status_code=404,
            detail=f"Job {job_id} not found"
        )

    if job_data["status"] != "completed":
        raise HTTPException(
            status_code=400,
            detail=f"Job {job_id} is not completed yet"
        )

    # Get job results
    results = automation.get_job_results(job_id)
    if not results:
        raise HTTPException(
            status_code=404,
            detail=f"No results found for job {job_id}"
        )

    # Prepare export data
    export_data = []
    for product_name, product_data in results.items():
        export_data.append({
            "product_name": product_name,
            "front_image": f"/api/products/images/{job_id}/{product_name}/front" if product_data.get("front_image") else None,
            "back_image": f"/api/products/images/{job_id}/{product_name}/back" if product_data.get("back_image") else None,
            "status": product_data.get("status", "unknown"),
            "error": product_data.get("error")
        })

    # Generate export file
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    if format.lower() == "json":
        # Create JSON file
        export_path = Path(settings.IMAGES_DIR) / job_id / f"results_{timestamp}.json"
        export_path.parent.mkdir(parents=True, exist_ok=True)

        with open(export_path, "w") as f:
            json.dump(export_data, f, indent=2)

        return FileResponse(
            str(export_path),
            media_type="application/json",
            filename=f"results_{job_id}.json"
        )

    elif format.lower() == "csv":
        # Create CSV file
        export_path = Path(settings.IMAGES_DIR) / job_id / f"results_{timestamp}.csv"
        export_path.parent.mkdir(parents=True, exist_ok=True)

        with open(export_path, "w", newline="") as f:
            writer = csv.DictWriter(f, fieldnames=["product_name", "front_image", "back_image", "status", "error"])
            writer.writeheader()
            writer.writerows(export_data)

        return FileResponse(
            str(export_path),
            media_type="text/csv",
            filename=f"results_{job_id}.csv"
        )

    else:
        raise HTTPException(
            status_code=400,
            detail="Invalid export format. Must be 'json' or 'csv'"
        )

@router.delete("/jobs/{job_id}", response_model=dict)
async def cancel_job(job_id: str) -> dict:
    """Cancel a processing job."""
    success = automation.cancel_job(job_id)
    if not success:
        raise HTTPException(
            status_code=404,
            detail=f"Job {job_id} not found or already completed"
        )

    return {"message": f"Job {job_id} cancelled successfully"}

@router.post("/storage/cleanup", response_model=dict)
async def cleanup_storage() -> dict:
    """Clean up old job files and directories."""
    cleaned_jobs = automation.cleanup_old_jobs()
    return {
        "message": f"Cleaned up {len(cleaned_jobs)} old jobs",
        "cleaned_jobs": cleaned_jobs
    }

@router.get("/storage/info", response_model=StorageInfoResponse)
async def get_storage_info() -> StorageInfoResponse:
    """Get storage usage information."""
    total_bytes = automation.get_storage_usage()
    return StorageInfoResponse(
        total_size_bytes=total_bytes,
        total_size_mb=round(total_bytes / (1024 * 1024), 2)
    )