from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends
from typing import Optional

from app.core.websocket_manager import WebSocketManager

router = APIRouter(prefix="/ws", tags=["websocket"])
websocket_manager = WebSocketManager()

@router.websocket("/jobs/{job_id}")
async def websocket_job_endpoint(websocket: WebSocket, job_id: str):
    """WebSocket endpoint for real-time job updates."""
    try:
        await websocket_manager.connect(websocket, job_id)
        while True:
            try:
                # Keep the connection alive and handle any client messages
                data = await websocket.receive_json()
                # Echo back to confirm receipt
                await websocket.send_json({"status": "received", "data": data})
            except WebSocketDisconnect:
                await websocket_manager.disconnect(websocket, job_id)
                break
    except Exception as e:
        await websocket_manager.disconnect(websocket, job_id)
        raise

# Export the websocket manager for use in other modules
get_websocket_manager = lambda: websocket_manager