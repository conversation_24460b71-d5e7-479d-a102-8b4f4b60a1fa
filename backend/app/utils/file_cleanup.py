import os
import shutil
from datetime import datetime, timedelta
from pathlib import Path
from typing import List

class FileCleanup:
    """Utility class for managing file cleanup operations."""

    def __init__(self, base_dir: str, max_age_days: int = 7):
        """Initialize the FileCleanup utility.

        Args:
            base_dir (str): Base directory for file cleanup
            max_age_days (int): Maximum age of files in days before cleanup
        """
        self.base_dir = Path(base_dir)
        self.max_age_days = max_age_days

    def cleanup_old_jobs(self) -> List[str]:
        """Clean up old job directories and files.

        Returns:
            List[str]: List of cleaned up job IDs
        """
        cleaned_jobs = []
        cutoff_date = datetime.now() - timedelta(days=self.max_age_days)

        # Ensure base directory exists
        if not self.base_dir.exists():
            return cleaned_jobs

        # Iterate through job directories
        for job_dir in self.base_dir.iterdir():
            if not job_dir.is_dir():
                continue

            try:
                # Check directory modification time
                mod_time = datetime.fromtimestamp(job_dir.stat().st_mtime)
                if mod_time < cutoff_date:
                    # Remove the directory and its contents
                    shutil.rmtree(job_dir)
                    cleaned_jobs.append(job_dir.name)
            except Exception as e:
                print(f"Error cleaning up job directory {job_dir}: {str(e)}")

        return cleaned_jobs

    def cleanup_job(self, job_id: str) -> bool:
        """Clean up a specific job's directory and files.

        Args:
            job_id (str): ID of the job to clean up

        Returns:
            bool: True if cleanup was successful, False otherwise
        """
        job_dir = self.base_dir / job_id

        if not job_dir.exists():
            return False

        try:
            shutil.rmtree(job_dir)
            return True
        except Exception as e:
            print(f"Error cleaning up job {job_id}: {str(e)}")
            return False

    def get_directory_size(self, directory: Path = None) -> int:
        """Get the total size of a directory in bytes.

        Args:
            directory (Path, optional): Directory to check. Defaults to base_dir.

        Returns:
            int: Total size in bytes
        """
        directory = directory or self.base_dir
        total_size = 0

        try:
            for entry in directory.rglob('*'):
                if entry.is_file():
                    total_size += entry.stat().st_size
        except Exception as e:
            print(f"Error calculating directory size: {str(e)}")

        return total_size