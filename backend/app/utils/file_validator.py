from typing import List, <PERSON>ple
import csv
import io

def validate_csv_file(content: bytes) -> Tuple[List[str], List[str]]:
    """Validate a CSV file and extract product names.

    Args:
        content (bytes): Raw content of the CSV file

    Returns:
        Tuple[List[str], List[str]]: A tuple containing:
            - List of valid product names
            - List of validation errors

    Raises:
        UnicodeDecodeError: If the file is not UTF-8 encoded
    """
    errors = []
    product_names = []

    # Decode content
    text_content = content.decode('utf-8')
    csv_reader = csv.reader(io.StringIO(text_content))

    # Process each row
    for row_num, row in enumerate(csv_reader, start=1):
        if not row:  # Skip empty rows
            continue

        # Get the product name from the first column
        product_name = row[0].strip()

        # Validate product name
        if not product_name:
            errors.append(f"Row {row_num}: Empty product name")
            continue

        if len(product_name) > 100:  # Maximum length validation
            errors.append(f"Row {row_num}: Product name exceeds 100 characters")
            continue

        # Add valid product name to list
        product_names.append(product_name)

    # Check if we found any valid products
    if not product_names and not errors:
        errors.append("CSV file is empty or contains no valid product names")

    return product_names, errors