import asyncio
import uuid
from enum import Enum
from typing import Dict, Optional, Any, Coroutine
from datetime import datetime

class JobStatus(str, Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class JobManager:
    def __init__(self):
        self.jobs: Dict[str, dict] = {}
        self.tasks: Dict[str, asyncio.Task] = {}

    def create_job(self, initial_data: dict) -> str:
        """Create a new job with initial data.

        Args:
            initial_data: Initial job metadata

        Returns:
            Job ID
        """
        job_id = str(uuid.uuid4())
        self.jobs[job_id] = {
            **initial_data,
            "status": JobStatus.PENDING,
            "progress": 0,
            "error": None,
            "results": {},
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
        return job_id

    def start_job(self, job_id: str, coro: Coroutine):
        """Start processing a job.

        Args:
            job_id: Job identifier
            coro: Coroutine to execute
        """
        if job_id not in self.jobs:
            raise ValueError(f"Job {job_id} not found")

        self.jobs[job_id]["status"] = JobStatus.RUNNING
        self.jobs[job_id]["updated_at"] = datetime.now().isoformat()
        task = asyncio.create_task(coro)
        self.tasks[job_id] = task

    def get_job(self, job_id: str) -> Optional[dict]:
        """Get job status and data."""
        return self.jobs.get(job_id)

    def update_job(self, job_id: str, data: dict):
        """Update job data.

        Args:
            job_id: Job identifier
            data: Data to update
        """
        if job_id in self.jobs:
            self.jobs[job_id].update(data)
            self.jobs[job_id]["updated_at"] = datetime.now().isoformat()

    def update_job_results(self, job_id: str, results: Dict):
        """Update job results.

        Args:
            job_id: Job identifier
            results: Results data to store
        """
        if job_id not in self.jobs:
            raise ValueError(f"Job {job_id} not found")
        self.jobs[job_id]["results"] = results
        self.jobs[job_id]["updated_at"] = datetime.now().isoformat()

    def cancel_job(self, job_id: str) -> bool:
        """Cancel a running job.

        Args:
            job_id: Job identifier

        Returns:
            Whether cancellation was successful
        """
        if job_id not in self.jobs or job_id not in self.tasks:
            return False

        task = self.tasks[job_id]
        if task.done():
            return False

        task.cancel()
        self.jobs[job_id]["status"] = JobStatus.CANCELLED
        self.jobs[job_id]["updated_at"] = datetime.now().isoformat()
        return True

    def cleanup_job(self, job_id: str):
        """Remove job data and task."""
        self.jobs.pop(job_id, None)
        self.tasks.pop(job_id, None)