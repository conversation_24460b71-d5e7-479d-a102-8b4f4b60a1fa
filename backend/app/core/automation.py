from typing import List, Optional, Dict
from pathlib import Path
from datetime import datetime

from .product_image_automation import ProductImageAutomation
from .models import ProductImageResult, BatchProcessingResult
from ..utils.job_manager import <PERSON><PERSON>anager, JobStatus
from ..utils.file_cleanup import FileCleanup
from .config import get_settings

settings = get_settings()

class AutomationWrapper:
    def __init__(self):
        self.automation = ProductImageAutomation()
        self.job_manager = JobManager()
        self.file_cleanup = FileCleanup(settings.IMAGES_DIR)

    async def create_job(self, product_names: List[str], search_front: bool = True,
                        search_back: bool = True) -> str:
        """Create a new processing job.

        Args:
            product_names: List of products to process
            search_front: Whether to search front views
            search_back: Whether to search back views

        Returns:
            Job ID for tracking progress
        """
        job_id = self.job_manager.create_job({
            "product_names": product_names,
            "search_front": search_front,
            "search_back": search_back,
            "created_at": datetime.utcnow().isoformat(),
            "total_products": len(product_names)
        })

        # Start processing in background
        self.job_manager.start_job(job_id, self._process_job(job_id))
        return job_id

    async def _process_job(self, job_id: str):
        """Process a batch job and update status."""
        try:
            job_data = self.job_manager.get_job(job_id)
            if not job_data:
                return

            # Process batch
            result = await self.automation.process_product_batch(
                product_names=job_data["product_names"],
                search_front=job_data["search_front"],
                search_back=job_data["search_back"]
            )

            # Update job with results
            self.job_manager.update_job(job_id, {
                "status": JobStatus.COMPLETED,
                "completed_at": datetime.utcnow().isoformat(),
                "result": result.dict()
            })

        except Exception as e:
            self.job_manager.update_job(job_id, {
                "status": JobStatus.FAILED,
                "error": str(e),
                "completed_at": datetime.utcnow().isoformat()
            })

    def get_job_status(self, job_id: str) -> Optional[dict]:
        """Get current status of a job."""
        return self.job_manager.get_job(job_id)

    def get_job_results(self, job_id: str) -> Optional[Dict]:
        """Get the results of a completed job.

        Args:
            job_id (str): Job ID

        Returns:
            Optional[Dict]: Job results or None if not found
        """
        job = self.job_manager.get_job(job_id)
        if job and job.get("status") == JobStatus.COMPLETED.value:
            return job.get("result", {})
        return None

    def cancel_job(self, job_id: str) -> bool:
        """Cancel a job and clean up its files.

        Args:
            job_id (str): Job ID

        Returns:
            bool: True if job was cancelled, False otherwise
        """
        if self.job_manager.cancel_job(job_id):
            # Clean up job files
            self.file_cleanup.cleanup_job(job_id)
            return True
        return False

    def cleanup_old_jobs(self) -> List[str]:
        """Clean up old job directories and files.

        Returns:
            List[str]: List of cleaned up job IDs
        """
        return self.file_cleanup.cleanup_old_jobs()

    def get_storage_usage(self) -> int:
        """Get total storage usage in bytes.

        Returns:
            int: Total storage size in bytes
        """
        return self.file_cleanup.get_directory_size()