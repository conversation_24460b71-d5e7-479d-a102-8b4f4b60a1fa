from typing import List, Optional, Dict
from pathlib import Path
from datetime import datetime
import asyncio

from .product_image_automation import ProductImageAutomation
from .models import ProductImageResult, BatchProcessingResult
from .websocket_manager import WebSocketManager
from .config import get_settings

settings = get_settings()

class AutomationWrapper:
    def __init__(self, websocket_manager: Optional[WebSocketManager] = None):
        self.automation = ProductImageAutomation()
        self.websocket_manager = websocket_manager
        self.jobs: Dict[str, dict] = {}

    def _generate_job_id(self) -> str:
        """Generate a unique job ID."""
        import uuid
        return str(uuid.uuid4())

    async def create_job(self, product_names: List[str], search_front: bool = True,
                        search_back: bool = True) -> str:
        """Create a new processing job.

        Args:
            product_names: List of products to process
            search_front: Whether to search front views
            search_back: Whether to search back views

        Returns:
            Job ID for tracking progress
        """
        job_id = self._generate_job_id()

        # Create job data
        job_data = {
            "job_id": job_id,
            "status": "queued",
            "created_at": datetime.now(),
            "completed_at": None,
            "total_products": len(product_names),
            "processed_products": 0,
            "successful_products": 0,
            "failed_products": 0,
            "progress": 0.0,
            "product_names": product_names,
            "search_front": search_front,
            "search_back": search_back,
            "error": None,
            "results": {}
        }

        self.jobs[job_id] = job_data

        # Start processing in background
        asyncio.create_task(self._process_job(job_id))

        # Send initial update
        await self._broadcast_update(job_id)

        return job_id

    async def _process_job(self, job_id: str):
        """Process a batch job and update status."""
        job = self.jobs.get(job_id)
        if not job:
            return

        try:
            job["status"] = "running"
            await self._broadcast_update(job_id)

            # Process each product individually to provide progress updates
            results = {}
            for i, product_name in enumerate(job["product_names"]):
                if job["status"] == "cancelled":
                    break

                try:
                    # Process single product
                    result = await self.automation.process_single_product(
                        product_name=product_name,
                        search_front=job["search_front"],
                        search_back=job["search_back"]
                    )

                    results[product_name] = {
                        "status": "completed",
                        "front_image": result.front_images[0].local_path if result.front_images else None,
                        "back_image": result.back_images[0].local_path if result.back_images else None,
                        "front_score": result.front_images[0].quality_score if result.front_images else 0.0,
                        "back_score": result.back_images[0].quality_score if result.back_images else 0.0,
                    }

                    job["successful_products"] += 1

                except Exception as e:
                    results[product_name] = {
                        "status": "failed",
                        "error": str(e)
                    }
                    job["failed_products"] += 1

                # Update progress
                job["processed_products"] += 1
                job["progress"] = (i + 1) / job["total_products"] * 100

                # Send progress update
                await self._broadcast_update(job_id)

            if job["status"] != "cancelled":
                job["status"] = "completed"
                job["completed_at"] = datetime.now()
                job["results"] = results

        except Exception as e:
            job["status"] = "failed"
            job["error"] = str(e)
            job["completed_at"] = datetime.now()

        # Send final update
        await self._broadcast_update(job_id)

    async def _broadcast_update(self, job_id: str):
        """Broadcast job status update to connected WebSocket clients."""
        if self.websocket_manager:
            job_data = self.jobs.get(job_id)
            if job_data:
                await self.websocket_manager.broadcast_job_update(job_id, job_data)

    def get_job_status(self, job_id: str) -> Optional[dict]:
        """Get current status of a job."""
        return self.jobs.get(job_id)

    def get_job_results(self, job_id: str) -> Optional[Dict]:
        """Get the results of a completed job.

        Args:
            job_id (str): Job ID

        Returns:
            Optional[Dict]: Job results or None if not found
        """
        job = self.jobs.get(job_id)
        if job and job.get("status") == "completed":
            return job.get("results", {})
        return None

    async def cancel_job(self, job_id: str) -> bool:
        """Cancel a job and clean up its files.

        Args:
            job_id (str): Job ID

        Returns:
            bool: True if job was cancelled, False otherwise
        """
        job = self.jobs.get(job_id)
        if job and job["status"] in ["queued", "running"]:
            job["status"] = "cancelled"
            job["error"] = "Job cancelled by user"
            job["completed_at"] = datetime.now()
            await self._broadcast_update(job_id)
            return True
        return False

    def cleanup_old_jobs(self) -> List[str]:
        """Clean up old job directories and files.

        Returns:
            List[str]: List of cleaned up job IDs
        """
        # TODO: Implement cleanup logic
        return []

    def get_storage_usage(self) -> int:
        """Get total storage usage in bytes.

        Returns:
            int: Total storage size in bytes
        """
        # TODO: Implement storage usage calculation
        return 0