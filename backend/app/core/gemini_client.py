"""Gemini AI client for image assessment using Google GenAI SDK."""

import asyncio
import io
import json
import re
import time
from typing import Optional, Dict, Any, List
from urllib.parse import urlparse
from io import BytesIO

import aiohttp
from PIL import Image
from google import genai
from google.genai import types

from .config import Settings
from .logger import LoggerMixin
from .models import ImageAssessment, ProductComponents, ViewType


class GeminiClient(LoggerMixin):
    """Client for interacting with Google Gemini AI for image assessment."""

    def __init__(self, settings: Settings, api_key: Optional[str] = None):
        """Initialize Gemini client.

        Args:
            settings: Application settings
            api_key: Gemini API key. If None, uses settings.gemini_api_key
        """
        self.settings = settings
        self.api_key = api_key or settings.GEMINI_API_KEY

        # Initialize the Gemini client with API key
        self.client = genai.Client(api_key=self.api_key)

        # Store model configuration
        self.model_name = settings.GEMINI_MODEL

        # Configure tools with URL context
        self.tools = [
            types.Tool(url_context=types.UrlContext()),
        ]
        self.tools = []  # Empty tools list

        self.generation_config = types.GenerateContentConfig(
            temperature=settings.GEMINI_TEMPERATURE,
            max_output_tokens=settings.GEMINI_MAX_TOKENS,
            tools=self.tools
        )

        # HTTP session for downloading images
        self.session: Optional[aiohttp.ClientSession] = None

    async def __aenter__(self):
        """Async context manager entry."""
        await self._ensure_session()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()

    async def _ensure_session(self):
        """Ensure aiohttp session is created."""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=self.settings.REQUEST_TIMEOUT)
            self.session = aiohttp.ClientSession(
                timeout=timeout,
                headers={'User-Agent': 'ProductImageAutomation/1.0'}
            )

    async def close(self):
        """Close the aiohttp session."""
        if self.session and not self.session.closed:
            await self.session.close()

    def _create_assessment_prompt(
        self,
        product_name: str,
        view_type: str,
        include_nutrition_check: bool = True
    ) -> str:
        """Create an enhanced prompt for image assessment with component extraction.

        Args:
            product_name: Name of the product
            view_type: Expected view type ('front' or 'back')
            include_nutrition_check: Whether to check for nutrition information

        Returns:
            Formatted prompt string
        """
        nutrition_scoring = ""
        if include_nutrition_check and view_type == "back":
            nutrition_scoring = """
    - nutrition_clarity: 0.0-10.0 (clarity of nutrition facts panel)
    - ingredients_visible: 0.0-10.0 (visibility of ingredients list)
    - barcode_visible: 0.0-10.0 (visibility and readability of barcode)"""
        else:
            nutrition_scoring = """
    - nutrition_clarity: 0.0 (set to 0 for front view)
    - ingredients_visible: 0.0 (set to 0 for front view)
    - barcode_visible: 0.0 (set to 0 for front view)"""

        return f"""
Analyze this image for a grocery/retail product called "{product_name}".
This should be a {view_type} view image of the product.

**STEP 1: PRODUCT COMPONENT EXTRACTION**
First, extract the following components from the product name "{product_name}":
- Brand: The manufacturer or brand name
- Product Type: The category/type of product (e.g., "cereal", "shampoo", "chips")
- Size/Variant: Any size, flavor, or variant information (e.g., "500ml", "chocolate", "family size")
- Keywords: Key identifiers for verification

**STEP 2: PRODUCT MATCH VERIFICATION**
Verify if the image shows the correct product by checking:
- Brand Match: Does the visible brand match the extracted brand?
- Product Type Match: Does the product type match?
- Size/Variant Match: If specified, does the size/variant match?

**CRITICAL RULE: If ANY component does not match, set ALL scores to 0 and product_match_verified to false.**

**STEP 3: QUALITY ASSESSMENT** (Only if product matches)
Evaluate these quality criteria (0.0-10.0 scale):
- brand_visibility: How clearly is the brand name visible?
- label_readability: How readable are labels and text?
- product_isolation: How well is the product isolated from background?
{nutrition_scoring}

**STEP 4: OVERALL ASSESSMENT**
- Image Quality (1-10): Resolution, clarity, lighting
- View Correctness: Is this the correct {view_type} view?
- Overall Score (1-10): Combined assessment

Respond ONLY with a valid JSON object in this exact format:
{{
    "product_components": {{
        "brand": "<extracted brand>",
        "product_type": "<extracted product type>",
        "size_variant": "<extracted size/variant or null>",
        "keywords": ["<key1>", "<key2>"]
    }},
    "product_match_verified": <true/false>,
    "brand_match": <true/false>,
    "product_type_match": <true/false>,
    "size_variant_match": <true/false/null>,
    "mismatch_reason": "<reason if no match, null if match>",
    "brand_visibility": <0.0-10.0>,
    "label_readability": <0.0-10.0>,
    "product_isolation": <0.0-10.0>,
    "nutrition_clarity": <0.0-10.0>,
    "ingredients_visible": <0.0-10.0>,
    "barcode_visible": <0.0-10.0>,
    "detected_text": ["<visible text1>", "<visible text2>"],
    "quality_issues": ["<issue1>", "<issue2>"],
    "quality_score": <1-10>,
    "product_match": <true/false>,
    "correct_view": <true/false>,
    "has_nutrition_info": <true/false/null>,
    "overall_score": <1-10>,
    "reasoning": "<brief explanation>"
}}
"""

    def _get_image_mime_type(self, image_data: bytes) -> str:
        """Detect MIME type from image data.

        Args:
            image_data: Raw image bytes

        Returns:
            MIME type string
        """
        try:
            with Image.open(io.BytesIO(image_data)) as img:
                format_lower = img.format.lower() if img.format else 'jpeg'
                if format_lower in ['jpeg', 'jpg']:
                    return 'image/jpeg'
                elif format_lower == 'png':
                    return 'image/png'
                elif format_lower == 'webp':
                    return 'image/webp'
                elif format_lower == 'gif':
                    return 'image/gif'
                else:
                    return 'image/jpeg'  # Default fallback
        except Exception:
            return 'image/jpeg'  # Default fallback

    async def _download_and_validate_image(self, image_url: str) -> Optional[tuple[bytes, str]]:
        """Download and validate an image from URL.

        Args:
            image_url: URL of the image to download

        Returns:
            Tuple of (image_bytes, mime_type) if successful, None if failed
        """
        await self._ensure_session()

        try:
            # Validate URL
            parsed_url = urlparse(image_url)
            if not parsed_url.scheme or not parsed_url.netloc:
                raise ValueError(f"Invalid URL: {image_url}")

            # Download image with domain-specific headers and retry logic
            user_agents = [
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
                'Mozilla/5.0 (iPad; CPU OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1'
            ]

            # Domain-specific header strategies
            domain = parsed_url.netloc.lower()

            # Base headers
            headers = {
                'User-Agent': user_agents[0],
                'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            }

            # Domain-specific customizations
            if 'coles.com.au' in domain:
                headers.update({
                    'Referer': 'https://shop.coles.com.au/',
                    'Origin': 'https://shop.coles.com.au',
                    'Sec-Fetch-Dest': 'image',
                    'Sec-Fetch-Mode': 'no-cors',
                    'Sec-Fetch-Site': 'same-origin',
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache'
                })
            elif 'woolworths.com.au' in domain:
                headers.update({
                    'Referer': 'https://www.woolworths.com.au/',
                    'Origin': 'https://www.woolworths.com.au',
                    'Sec-Fetch-Dest': 'image',
                    'Sec-Fetch-Mode': 'no-cors',
                    'Sec-Fetch-Site': 'same-origin'
                })
            elif 'amazon.' in domain:
                headers.update({
                    'Referer': f'https://{domain}/',
                    'Sec-Fetch-Dest': 'image',
                    'Sec-Fetch-Mode': 'no-cors',
                    'Sec-Fetch-Site': 'same-origin'
                })
            else:
                # Generic headers for other domains
                headers.update({
                    'Referer': 'https://www.google.com/',
                    'Sec-Fetch-Dest': 'image',
                    'Sec-Fetch-Mode': 'no-cors',
                    'Sec-Fetch-Site': 'cross-site'
                })

            # Retry logic for handling temporary failures
            max_retries = 3
            base_delay = 1.0

            for attempt in range(max_retries):
                 try:
                     # Try different user agents on retries for 403 errors
                     if attempt > 0:
                         headers['User-Agent'] = user_agents[attempt % len(user_agents)]

                     async with self.session.get(image_url, headers=headers, timeout=aiohttp.ClientTimeout(total=30)) as response:
                        if response.status == 403:
                            if attempt < max_retries - 1:
                                self.logger.warning(f"403 error on attempt {attempt + 1}, trying different user agent")
                                await asyncio.sleep(base_delay * (2 ** attempt))
                                continue
                            else:
                                raise aiohttp.ClientResponseError(
                                    request_info=response.request_info,
                                    history=response.history,
                                    status=response.status,
                                    message=f"Access forbidden (403) for image URL: {image_url}. The image may be protected or require authentication."
                                )
                        elif response.status == 404:
                            raise aiohttp.ClientResponseError(
                                request_info=response.request_info,
                                history=response.history,
                                status=response.status,
                                message=f"Image not found (404) at URL: {image_url}"
                            )
                        elif response.status in [429, 503, 502, 504]:  # Rate limiting or server errors
                            if attempt < max_retries - 1:
                                delay = base_delay * (2 ** attempt)  # Exponential backoff
                                await asyncio.sleep(delay)
                                continue
                            else:
                                raise aiohttp.ClientResponseError(
                                    request_info=response.request_info,
                                    history=response.history,
                                    status=response.status,
                                    message=f"HTTP {response.status} error downloading image from: {image_url} (after {max_retries} retries)"
                                )
                        elif response.status != 200:
                            raise aiohttp.ClientResponseError(
                                request_info=response.request_info,
                                history=response.history,
                                status=response.status,
                                message=f"HTTP {response.status} error downloading image from: {image_url}"
                            )

                        # Check content type
                        content_type = response.headers.get('content-type', '').lower()
                        if not content_type.startswith('image/'):
                            raise ValueError(f"URL does not point to an image: {content_type}")

                        # Read image data
                        image_data = await response.read()

                        # Validate image can be opened and get MIME type
                        try:
                            with Image.open(io.BytesIO(image_data)) as img:
                                # Basic validation
                                if img.width < 100 or img.height < 100:
                                    raise ValueError("Image too small (minimum 100x100 pixels)")

                                # Check file size (max 10MB)
                                if len(image_data) > 10 * 1024 * 1024:
                                    raise ValueError("Image too large (maximum 10MB)")

                        except Exception as e:
                            raise ValueError(f"Invalid image format: {str(e)}")

                        # Get proper MIME type
                        mime_type = self._get_image_mime_type(image_data)
                        return image_data, mime_type

                 except (aiohttp.ClientError, asyncio.TimeoutError) as e:
                     if attempt < max_retries - 1:
                         delay = base_delay * (2 ** attempt)  # Exponential backoff
                         await asyncio.sleep(delay)
                         continue
                     else:
                         self.logger.error(f"Failed to download image after {max_retries} attempts: {str(e)}")
                         return None

        except (aiohttp.ClientResponseError, Exception) as e:
            # Handle 403 errors and other exceptions gracefully
            if isinstance(e, aiohttp.ClientResponseError) and e.status == 403:
                self.logger.warning(f"Access forbidden (403) for image URL: {image_url}. Proceeding with text-only assessment.")
            else:
                self.log_error(e, "image_download", url=image_url)
            return None

    def _parse_assessment_response(self, response_text: str) -> Dict[str, Any]:
        """Parse Gemini's enhanced JSON response with fallback handling.

        Args:
            response_text: Raw response text from Gemini

        Returns:
            Parsed assessment dictionary
        """
        try:
            # Try to find JSON in the response
            response_text = response_text.strip()

            # First, check if response is wrapped in markdown code blocks
            if '```json' in response_text:
                # Extract JSON from markdown code block
                start_marker = response_text.find('```json')
                if start_marker != -1:
                    # Find the start of JSON content after the marker
                    json_start = response_text.find('\n', start_marker) + 1
                    if json_start > 0:
                        # Find the end marker
                        end_marker = response_text.find('```', json_start)
                        if end_marker != -1:
                            json_str = response_text[json_start:end_marker].strip()
                        else:
                            # No closing marker, take everything after the opening
                            json_str = response_text[json_start:].strip()
                            # Try to find natural JSON end if truncated
                            if '{' in json_str:
                                json_start_idx = json_str.find('{')
                                json_str = json_str[json_start_idx:]
                    else:
                        # Fallback to looking for JSON boundaries
                        json_str = self._extract_json_boundaries(response_text)
                else:
                    json_str = self._extract_json_boundaries(response_text)
            else:
                # No markdown formatting, use standard extraction
                json_str = self._extract_json_boundaries(response_text)

            # Additional cleanup for common Gemini response patterns
            if json_str:
                # Remove any leading/trailing whitespace
                json_str = json_str.strip()

                # Handle cases where response starts with explanation text
                if not json_str.startswith('{'):
                    brace_idx = json_str.find('{')
                    if brace_idx != -1:
                        json_str = json_str[brace_idx:]

                # Handle cases where JSON is followed by explanation text
                if '}' in json_str:
                    last_brace = json_str.rfind('}')
                    # Check if there's significant content after the last brace
                    remaining = json_str[last_brace + 1:].strip()
                    if remaining and not remaining.startswith('```'):
                        # Keep only up to the last meaningful closing brace
                        json_str = json_str[:last_brace + 1]

            if not json_str:
                raise ValueError("No JSON object found in response")

            # Try to fix common JSON formatting issues
            json_str = self._fix_json_formatting(json_str)

            # Validate JSON structure before parsing
            if not self._is_valid_json_structure(json_str):
                raise ValueError("Invalid JSON structure detected")

            parsed = json.loads(json_str)

            # Validate required fields
            required_fields = [
                'quality_score', 'product_match', 'correct_view',
                'overall_score', 'reasoning'
            ]

            for field in required_fields:
                if field not in parsed:
                    raise ValueError(f"Missing required field: {field}")

            # Validate score ranges
            for score_field in ['quality_score', 'overall_score']:
                score = parsed[score_field]
                if not isinstance(score, int) or not 1 <= score <= 10:
                    parsed[score_field] = max(1, min(10, int(score) if isinstance(score, (int, float)) else 5))

            # Validate boolean fields
            for bool_field in ['product_match', 'correct_view']:
                if not isinstance(parsed[bool_field], bool):
                    parsed[bool_field] = bool(parsed[bool_field])

            # Handle nutrition info field (can be null)
            if 'has_nutrition_info' in parsed:
                nutrition_val = parsed['has_nutrition_info']
                if nutrition_val is not None and not isinstance(nutrition_val, bool):
                    parsed['has_nutrition_info'] = bool(nutrition_val) if nutrition_val != 'null' else None

            # Validate enhanced fields with defaults
            # Product components
            if 'product_components' not in parsed:
                parsed['product_components'] = {
                    'brand': '',
                    'product_type': '',
                    'size_variant': None,
                    'keywords': []
                }
            else:
                # Ensure required fields are not None
                components = parsed['product_components']
                if components.get('brand') is None:
                    components['brand'] = ''
                if components.get('product_type') is None:
                    components['product_type'] = ''
                if not isinstance(components.get('keywords'), list):
                    components['keywords'] = []

            # Component verification fields
            parsed['product_match_verified'] = parsed.get('product_match_verified', False)
            parsed['brand_match'] = parsed.get('brand_match', False)
            parsed['product_type_match'] = parsed.get('product_type_match', False)
            parsed['size_variant_match'] = parsed.get('size_variant_match', None)
            parsed['mismatch_reason'] = parsed.get('mismatch_reason', None)

            # Quality scoring fields (0.0-10.0 range)
            quality_fields = [
                'brand_visibility', 'label_readability', 'product_isolation',
                'nutrition_clarity', 'ingredients_visible', 'barcode_visible'
            ]
            for field in quality_fields:
                value = parsed.get(field, 0.0)
                if not isinstance(value, (int, float)) or not 0.0 <= value <= 10.0:
                    parsed[field] = max(0.0, min(10.0, float(value) if isinstance(value, (int, float)) else 0.0))
                else:
                    parsed[field] = float(value)

            # List fields
            parsed['detected_text'] = parsed.get('detected_text', [])
            if not isinstance(parsed['detected_text'], list):
                parsed['detected_text'] = []

            parsed['quality_issues'] = parsed.get('quality_issues', [])
            if not isinstance(parsed['quality_issues'], list):
                parsed['quality_issues'] = []

            return parsed

        except json.JSONDecodeError as e:
            # Log more detailed information about the JSON parsing failure
            self.log_error(
                e,
                "json_decode_error",
                response=response_text[:500],  # Show more context
                json_str=json_str[:300] if 'json_str' in locals() else "N/A",
                error_details=str(e),
                error_position=f"line {e.lineno}, column {e.colno}" if hasattr(e, 'lineno') else "unknown"
            )
            return self._create_fallback_response(f"JSON decode error at {getattr(e, 'lineno', '?')}:{getattr(e, 'colno', '?')}: {str(e)}")
        except Exception as e:
            self.log_error(
                e,
                "response_parsing",
                response=response_text[:500],
                json_str=json_str[:300] if 'json_str' in locals() else "N/A"
            )
            return self._create_fallback_response(f"Could not parse AI response: {str(e)}")

    def _is_valid_json_structure(self, json_str: str) -> bool:
        """Perform basic validation of JSON structure.

        Args:
            json_str: JSON string to validate

        Returns:
            True if structure appears valid, False otherwise
        """
        if not json_str or not json_str.strip():
            return False

        json_str = json_str.strip()

        # Must start and end with braces
        if not (json_str.startswith('{') and json_str.endswith('}')):
            return False

        # Check for balanced braces and brackets
        brace_count = 0
        bracket_count = 0
        in_string = False
        escape_next = False

        for char in json_str:
            if escape_next:
                escape_next = False
                continue

            if char == '\\' and in_string:
                escape_next = True
                continue

            if char == '"' and not escape_next:
                in_string = not in_string
                continue

            if not in_string:
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                elif char == '[':
                    bracket_count += 1
                elif char == ']':
                    bracket_count -= 1

        return brace_count == 0 and bracket_count == 0

    def _extract_json_boundaries(self, response_text: str) -> str:
        """Extract JSON content by finding object boundaries.

        Args:
            response_text: Raw response text

        Returns:
            Extracted JSON string or empty string if not found
        """
        # Look for JSON object boundaries
        start_idx = response_text.find('{')
        end_idx = response_text.rfind('}') + 1

        if start_idx == -1 or end_idx == 0:
            return ""

        return response_text[start_idx:end_idx]

    def _fix_json_formatting(self, json_str: str) -> str:
        """Attempt to fix common JSON formatting issues.

        Args:
            json_str: Raw JSON string that may have formatting issues

        Returns:
            Fixed JSON string
        """
        try:
            # Remove any markdown artifacts that might remain
            json_str = re.sub(r'^```json\s*', '', json_str)
            json_str = re.sub(r'\s*```$', '', json_str)
            json_str = json_str.strip()

            # Remove any trailing commas before closing braces/brackets
            json_str = re.sub(r',\s*([}\]])', r'\1', json_str)

            # Fix missing commas between object properties (more conservative)
            json_str = re.sub(r'"\s*\n\s*"([^:]*?)"\s*:', r'",\n"\1":', json_str)

            # Fix property names that might not be quoted (more precise)
            json_str = re.sub(r'([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:', r'\1"\2":', json_str)

            # Fix single quotes to double quotes for property names and string values
            json_str = re.sub(r"'([^']*?)'\s*:", r'"\1":', json_str)  # Property names
            json_str = re.sub(r":\s*'([^']*?)'", r': "\1"', json_str)  # String values

            # Handle truncated JSON - try to complete incomplete structures
            if json_str and not json_str.endswith('}'):
                # Check if we're in the middle of a string value
                if json_str.count('"') % 2 == 1:
                    # Odd number of quotes means we're in an unclosed string
                    json_str += '"'

                # Check if we're in the middle of an array
                if '[' in json_str and json_str.rfind('[') > json_str.rfind(']'):
                    # Unclosed array - try to close it
                    if not json_str.rstrip().endswith(','):
                        json_str = json_str.rstrip() + ']'
                    else:
                        json_str = json_str.rstrip().rstrip(',') + ']'

            # Handle incomplete JSON by ensuring proper closing
            open_braces = json_str.count('{')
            close_braces = json_str.count('}')
            if open_braces > close_braces:
                # Add missing closing braces
                missing_braces = open_braces - close_braces
                # If we're in the middle of a property, add a placeholder value
                if json_str.rstrip().endswith(':'):
                    json_str += ' "incomplete"'
                elif json_str.rstrip().endswith(','):
                    json_str = json_str.rstrip().rstrip(',')

                # Try to complete missing required fields before closing
                required_fields = [
                    'quality_score', 'product_match', 'correct_view',
                    'overall_score', 'reasoning'
                ]

                # Parse what we have so far to check for missing fields
                try:
                    temp_json = json_str + '}' * missing_braces
                    temp_parsed = json.loads(temp_json)

                    # Add missing required fields with default values
                    missing_additions = []
                    for field in required_fields:
                        if field not in temp_parsed:
                            if field in ['quality_score', 'overall_score']:
                                missing_additions.append(f'"{field}": 5')
                            elif field in ['product_match', 'correct_view']:
                                missing_additions.append(f'"{field}": true')
                            elif field == 'reasoning':
                                missing_additions.append(f'"{field}": "Response truncated"')

                    if missing_additions:
                        # Add comma if needed
                        if not json_str.rstrip().endswith(',') and not json_str.rstrip().endswith('{'):
                            json_str += ','
                        json_str += ', '.join(missing_additions)

                except (json.JSONDecodeError, ValueError):
                    # If temp parsing fails, just add the closing braces
                    pass

                json_str += '}' * missing_braces

            # Handle incomplete arrays
            open_brackets = json_str.count('[')
            close_brackets = json_str.count(']')
            if open_brackets > close_brackets:
                json_str += ']' * (open_brackets - close_brackets)

            return json_str
        except Exception:
            # If fixing fails, return original
            return json_str

    def _create_fallback_response(self, error_message: str) -> Dict[str, Any]:
        """Create a standardized fallback response for parsing errors.

        Args:
            error_message: Error message to include in reasoning

        Returns:
            Fallback response dictionary
        """
        return {
            "quality_score": 5,
            "product_match": True,
            "correct_view": True,
            "has_nutrition_info": None,
            "overall_score": 5,
            "reasoning": error_message,
            "product_components": {
                "brand": "",
                "product_type": "",
                "size_variant": None,
                "keywords": []
            },
            "product_match_verified": False,
            "brand_match": False,
            "product_type_match": False,
            "size_variant_match": None,
            "mismatch_reason": error_message,
            "brand_visibility": 0.0,
            "label_readability": 0.0,
            "product_isolation": 0.0,
            "nutrition_clarity": 0.0,
            "ingredients_visible": 0.0,
            "barcode_visible": 0.0,
            "detected_text": [],
            "quality_issues": [error_message]
        }

    async def assess_image(
        self,
        image_url: str,
        product_name: str,
        view_type: str = "front"
    ) -> ImageAssessment:
        """Assess an image using Gemini AI.

        Args:
            image_url: URL of the image to assess
            product_name: Name of the product
            view_type: Expected view type ('front' or 'back')

        Returns:
            ImageAssessment object with AI evaluation results
        """
        start_time = time.time()

        try:
            # Create assessment prompt
            prompt = self._create_assessment_prompt(
                product_name=product_name,
                view_type=view_type,
                include_nutrition_check=(view_type == "back")
            )

            self.log_operation(
                "gemini_assessment_start",
                product=product_name,
                view_type=view_type,
                image_url=str(image_url)[:100] + "..." if len(str(image_url)) > 100 else str(image_url)
            )

            # Download and validate the image first
            image_result = await self._download_and_validate_image(image_url)

            if image_result is None:
                # Image download failed, perform text-only assessment with fallback
                self.logger.warning(f"Image download failed for {image_url}, performing text-only assessment")

                # Create text-only content for assessment
                text_only_prompt = f"""
Unable to download image from URL: {image_url}
Product: {product_name}
Expected view: {view_type}

Since the image cannot be accessed, provide a fallback assessment based on the URL and product information.
Respond with a JSON object indicating the image is inaccessible but maintain the required structure.

Respond ONLY with a valid JSON object in this exact format:
{{
    "product_components": {{
        "brand": "Unknown",
        "product_type": "Unknown",
        "size_variant": null,
        "keywords": []
    }},
    "product_match_verified": false,
    "brand_match": false,
    "product_type_match": false,
    "size_variant_match": null,
    "mismatch_reason": "Image inaccessible - cannot verify product match",
    "brand_visibility": 0.0,
    "label_readability": 0.0,
    "product_isolation": 0.0,
    "nutrition_clarity": 0.0,
    "ingredients_visible": 0.0,
    "barcode_visible": 0.0,
    "detected_text": [],
    "quality_issues": ["Image download failed", "URL inaccessible"],
    "quality_score": 1,
    "product_match": false,
    "correct_view": false,
    "has_nutrition_info": null,
    "overall_score": 1,
    "reasoning": "Image could not be downloaded from the provided URL. This may be due to access restrictions, invalid URL, or server issues."
}}
"""

                contents = [
                    types.Content(
                        role="user",
                        parts=[types.Part.from_text(text=text_only_prompt)]
                    )
                ]
            else:
                # Image download successful, proceed with normal assessment
                image_data, mime_type = image_result

                # Create content with text and image data using Content structure
                contents = [
                    types.Content(
                        role="user",
                        parts=[
                            types.Part.from_text(text=prompt),
                            types.Part.from_bytes(
                                data=image_data,
                                mime_type=mime_type
                            )
                        ],
                    ),
                ]

            # Generate response using new API with URL context
            response = await asyncio.to_thread(
                self.client.models.generate_content,
                model=self.model_name,
                contents=contents,
                config=self.generation_config
            )

            # Log the raw JSON response from Gemini
            self.log_operation(
                "gemini_response",
                product=product_name,
                view_type=view_type,
                response_text=response.text
            )
            print(f"Gemini JSON Response: {response.text}")

            # Parse response
            assessment_data = self._parse_assessment_response(response.text)

            # Create ProductComponents object
            product_components_data = assessment_data.get('product_components', {})
            product_components = ProductComponents(
                brand=product_components_data.get('brand', ''),
                product_type=product_components_data.get('product_type', ''),
                size_variant=product_components_data.get('size_variant'),
                keywords=product_components_data.get('keywords', [])
            )

            # Create ImageAssessment object with enhanced fields
            assessment = ImageAssessment(
                image_url=image_url,
                quality_score=assessment_data['quality_score'],
                product_match=assessment_data['product_match'],
                correct_view=assessment_data['correct_view'],
                has_nutrition_info=assessment_data.get('has_nutrition_info'),
                overall_score=assessment_data['overall_score'],
                reasoning=assessment_data['reasoning'],
                view_type=ViewType(view_type) if view_type in ViewType.__members__.values() else ViewType.UNKNOWN,
                processing_time=time.time() - start_time,
                # Enhanced component verification fields
                product_components=product_components,
                product_match_verified=assessment_data.get('product_match_verified', False),
                brand_match=assessment_data.get('brand_match', False),
                product_type_match=assessment_data.get('product_type_match', False),
                size_variant_match=assessment_data.get('size_variant_match'),
                mismatch_reason=assessment_data.get('mismatch_reason'),
                # Detailed quality scoring fields
                brand_visibility=assessment_data.get('brand_visibility', 0.0),
                label_readability=assessment_data.get('label_readability', 0.0),
                product_isolation=assessment_data.get('product_isolation', 0.0),
                nutrition_clarity=assessment_data.get('nutrition_clarity', 0.0),
                ingredients_visible=assessment_data.get('ingredients_visible', 0.0),
                barcode_visible=assessment_data.get('barcode_visible', 0.0),
                detected_text=assessment_data.get('detected_text', []),
                quality_issues=assessment_data.get('quality_issues', [])
            )

            self.log_operation(
                "gemini_assessment_success",
                product=product_name,
                view_type=view_type,
                overall_score=assessment.overall_score,
                processing_time=assessment.processing_time
            )

            return assessment

        except Exception as e:
            processing_time = time.time() - start_time

            self.log_error(
                e,
                "gemini_assessment",
                product=product_name,
                view_type=view_type,
                image_url=str(image_url)[:100] + "..." if len(str(image_url)) > 100 else str(image_url),
                processing_time=processing_time
            )

            # Create empty ProductComponents for error case
            empty_components = ProductComponents(
                brand="",
                product_type="",
                size_variant=None,
                keywords=[]
            )

            # Return error assessment with all enhanced fields
            return ImageAssessment(
                image_url=image_url,
                quality_score=1,
                product_match=False,
                correct_view=False,
                has_nutrition_info=None,
                overall_score=1,
                reasoning=f"Error during assessment: {str(e)}",
                view_type=ViewType(view_type) if view_type in ViewType.__members__.values() else ViewType.UNKNOWN,
                processing_time=processing_time,
                error_message=str(e),
                # Enhanced component verification fields with default values
                product_components=empty_components,
                product_match_verified=False,
                brand_match=False,
                product_type_match=False,
                size_variant_match=None,
                mismatch_reason=f"Error: {str(e)}",
                # Detailed quality scoring fields with default values
                brand_visibility=0.0,
                label_readability=0.0,
                product_isolation=0.0,
                nutrition_clarity=0.0,
                ingredients_visible=0.0,
                barcode_visible=0.0,
                detected_text=[],
                quality_issues=[f"Error: {str(e)}"]
            )

    async def assess_multiple_images(
        self,
        image_urls: List[str],
        product_name: str,
        view_type: str = "front",
        max_concurrent: int = None
    ) -> List[ImageAssessment]:
        """Assess multiple images concurrently.

        Args:
            image_urls: List of image URLs to assess
            product_name: Name of the product
            view_type: Expected view type
            max_concurrent: Maximum concurrent requests (defaults to settings)

        Returns:
            List of ImageAssessment objects
        """
        if not image_urls:
            return []

        max_concurrent = max_concurrent or self.settings.MAX_CONCURRENT_REQUESTS

        # Create semaphore to limit concurrent requests
        semaphore = asyncio.Semaphore(max_concurrent)

        async def assess_with_semaphore(url: str) -> ImageAssessment:
            async with semaphore:
                return await self.assess_image(url, product_name, view_type)

        # Process all images concurrently
        tasks = [assess_with_semaphore(url) for url in image_urls]
        assessments = await asyncio.gather(*tasks, return_exceptions=True)

        # Handle exceptions
        results = []
        for i, result in enumerate(assessments):
            if isinstance(result, Exception):
                self.log_error(result, "batch_assessment", image_index=i)
                # Create empty ProductComponents for batch error case
                empty_components = ProductComponents(
                    brand="",
                    product_type="",
                    size_variant=None,
                    keywords=[]
                )

                # Create error assessment with enhanced fields
                results.append(ImageAssessment(
                    image_url=image_urls[i],
                    quality_score=1,
                    product_match=False,
                    correct_view=False,
                    has_nutrition_info=None,
                    overall_score=1,
                    reasoning=f"Batch processing error: {str(result)}",
                    view_type=ViewType(view_type) if view_type in ViewType.__members__.values() else ViewType.UNKNOWN,
                    error_message=str(result),
                    # Enhanced component verification fields with default values
                    product_components=empty_components,
                    product_match_verified=False,
                    brand_match=False,
                    product_type_match=False,
                    size_variant_match=None,
                    mismatch_reason=f"Batch error: {str(result)}",
                    # Detailed quality scoring fields with default values
                    brand_visibility=0.0,
                    label_readability=0.0,
                    product_isolation=0.0,
                    nutrition_clarity=0.0,
                    ingredients_visible=0.0,
                    barcode_visible=0.0,
                    detected_text=[],
                    quality_issues=[f"Batch error: {str(result)}"]
                ))
            else:
                results.append(result)

        return results

    async def test_connection(self) -> bool:
        """Test connection to Gemini API.

        Returns:
            True if connection is successful, False otherwise
        """
        try:
            # Simple text generation test using Content structure
            test_contents = [
                types.Content(
                    role="user",
                    parts=[
                        types.Part.from_text(text="Hello, this is a test. Please respond with 'OK'.")
                    ],
                ),
            ]
            response = await asyncio.to_thread(
                self.client.models.generate_content,
                model=self.model_name,
                contents=test_contents,
                config=self.generation_config
            )
            return "OK" in response.text or "ok" in response.text.lower()
        except Exception as e:
            self.log_error(e, "connection_test")
            return False