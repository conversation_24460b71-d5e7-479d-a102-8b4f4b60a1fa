"""Serper.dev API client for image search functionality."""

import asyncio
from typing import List, Dict, Any, Optional
import aiohttp
from aiohttp import ClientTimeout, ClientError

from .config import settings
from .logger import LoggerMixin
from .models import SearchResult, SerperSearchResponse, ViewType


class SerperClient(LoggerMixin):
    """Client for interacting with Serper.dev API."""

    BASE_URL = "https://google.serper.dev"

    def __init__(self, api_key: Optional[str] = None):
        """Initialize Serper client.

        Args:
            api_key: Serper.dev API key. If None, uses settings.SERPER_API_KEY
        """
        self.api_key = api_key or settings.SERPER_API_KEY
        self.base_url = self.BASE_URL
        self.session: Optional[aiohttp.ClientSession] = None
        self.timeout = ClientTimeout(total=settings.REQUEST_TIMEOUT)

    async def __aenter__(self):
        """Async context manager entry."""
        await self._ensure_session()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()

    async def _ensure_session(self):
        """Ensure aiohttp session is created."""
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession(
                timeout=self.timeout,
                headers={
                    'X-API-KEY': self.api_key,
                    'Content-Type': 'application/json',
                    'User-Agent': 'ProductImageAutomation/1.0'
                }
            )

    async def close(self):
        """Close the aiohttp session."""
        if self.session and not self.session.closed:
            await self.session.close()

    async def search_images(
        self,
        query: str,
        num_results: int = 10,
        image_size: str = "large",
        safe_search: bool = True,
        **kwargs
    ) -> SerperSearchResponse:
        """Search for images using Serper.dev API.

        Args:
            query: Search query
            num_results: Number of results to return (1-100)
            image_size: Image size filter ('small', 'medium', 'large', 'xlarge')
            safe_search: Enable safe search
            **kwargs: Additional search parameters

        Returns:
            SerperSearchResponse containing search results

        Raises:
            aiohttp.ClientError: If API request fails
            ValueError: If parameters are invalid
        """
        await self._ensure_session()

        # Validate parameters
        if not query.strip():
            raise ValueError("Query cannot be empty")

        if not 1 <= num_results <= 100:
            raise ValueError("num_results must be between 1 and 100")

        # Build search parameters
        params = {
            'q': query.strip(),
            'num': min(num_results, settings.MAX_IMAGES_PER_SEARCH),
            'gl': 'us',  # Geographic location
            'hl': 'en',  # Language
        }

        # Add image-specific parameters
        tbs_params = []

        # Image size filter
        size_map = {
            'small': 'isz:s',
            'medium': 'isz:m',
            'large': 'isz:l',
            'xlarge': 'isz:xl'
        }
        if image_size in size_map:
            tbs_params.append(size_map[image_size])

        # Safe search
        if safe_search:
            params['safe'] = 'active'

        # Add custom tbs parameter
        if tbs_params:
            params['tbs'] = ','.join(tbs_params)

        # Add any additional parameters
        params.update(kwargs)

        self.log_operation(
            "serper_image_search",
            query=query,
            num_results=num_results,
            image_size=image_size
        )

        try:
            async with self.session.post(
                f"{self.BASE_URL}/images",
                json=params
            ) as response:
                response.raise_for_status()
                data = await response.json()

                # Parse response
                search_response = SerperSearchResponse.model_validate(data)

                self.log_operation(
                    "serper_search_success",
                    query=query,
                    results_count=len(search_response.images)
                )

                return search_response

        except ClientError as e:
            self.log_error(e, "serper_image_search", query=query)
            raise
        except Exception as e:
            self.log_error(e, "serper_image_search", query=query)
            raise

    async def search_product_images(
        self,
        product_name: str,
        search_front: bool = True,
        search_back: bool = True,
        max_images_per_view: int = 10
    ) -> Dict[str, List[SearchResult]]:
        """Search for product images with specific view types.

        Args:
            product_name: Name of the product
            search_front: Whether to search for front view images
            search_back: Whether to search for back view images
            max_images_per_view: Maximum images per view type

        Returns:
            Dictionary with 'front' and 'back' keys containing search results
        """
        results = {'front': [], 'back': []}

        # Search for front view images
        if search_front:
            front_queries = [
                f"{product_name} product front view package",
                f"{product_name} product packaging front",
                f"{product_name} front label"
            ]

            for query in front_queries:
                try:
                    response = await self.search_images(
                        query=query,
                        num_results=max_images_per_view // len(front_queries) + 1,
                        image_size="large"
                    )
                    results['front'].extend(response.images)

                    # Break if we have enough results
                    if len(results['front']) >= max_images_per_view:
                        results['front'] = results['front'][:max_images_per_view]
                        break

                except Exception as e:
                    self.log_error(e, "front_view_search", product=product_name, query=query)
                    continue

        # Search for back view images with nutrition info
        if search_back:
            back_queries = [
                f"{product_name} nutrition facts label back",
                f"{product_name} ingredients list back view",
                f"{product_name} product back nutrition information",
                f"{product_name} back package label"
            ]

            for query in back_queries:
                try:
                    response = await self.search_images(
                        query=query,
                        num_results=max_images_per_view // len(back_queries) + 1,
                        image_size="large"
                    )
                    results['back'].extend(response.images)

                    # Break if we have enough results
                    if len(results['back']) >= max_images_per_view:
                        results['back'] = results['back'][:max_images_per_view]
                        break

                except Exception as e:
                    self.log_error(e, "back_view_search", product=product_name, query=query)
                    continue

        # Remove duplicates based on image URL
        results['front'] = self._remove_duplicates(results['front'])
        results['back'] = self._remove_duplicates(results['back'])

        self.log_operation(
            "product_search_complete",
            product=product_name,
            front_count=len(results['front']),
            back_count=len(results['back'])
        )

        return results

    def _remove_duplicates(self, search_results: List[SearchResult]) -> List[SearchResult]:
        """Remove duplicate search results based on image URL.

        Args:
            search_results: List of search results

        Returns:
            List of unique search results
        """
        seen_urls = set()
        unique_results = []

        for result in search_results:
            url_str = str(result.image_url)
            if url_str not in seen_urls:
                seen_urls.add(url_str)
                unique_results.append(result)

        return unique_results

    async def test_connection(self) -> bool:
        """Test connection to Serper.dev API.

        Returns:
            True if connection is successful, False otherwise
        """
        try:
            response = await self.search_images(
                query="test",
                num_results=1
            )
            return len(response.images) >= 0  # Even 0 results is a successful connection
        except Exception as e:
            self.log_error(e, "connection_test")
            return False