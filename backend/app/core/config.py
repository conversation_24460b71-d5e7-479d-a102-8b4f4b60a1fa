from functools import lru_cache
from pydantic_settings import BaseSettings
from pathlib import Path

class Settings(BaseSettings):
    """Application settings.

    Attributes:
        PROJECT_NAME: Name of the project
        VERSION: API version
        API_PREFIX: Prefix for all API endpoints
        DEBUG: Debug mode flag
        IMAGES_DIR: Directory for storing processed images
        MAX_UPLOAD_SIZE: Maximum file upload size in bytes (10MB)
        OUTPUT_DIR: Directory for storing output files and logs
        LOG_LEVEL: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        LOG_FORMAT: Logging format (text or json)
        SERPER_API_KEY: API key for Serper.dev service
        GEMINI_API_KEY: API key for Google Gemini service
    """
    PROJECT_NAME: str = "Product Image Finder"
    VERSION: str = "1.0.0"
    API_PREFIX: str = "/api"
    DEBUG: bool = True

    # File storage settings
    IMAGES_DIR: Path = Path("data/images")
    OUTPUT_DIR: Path = Path("data/output")
    MAX_UPLOAD_SIZE: int = 10 * 1024 * 1024  # 10MB

    # Logging settings
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "text"

    # API keys
    SERPER_API_KEY: str = ""
    GEMINI_API_KEY: str

    # Gemini settings
    GEMINI_MODEL: str
    GEMINI_TEMPERATURE: float
    GEMINI_MAX_TOKENS: int

    # Request settings
    REQUEST_TIMEOUT: int = 30  # seconds
    MAX_IMAGES_PER_SEARCH: int = 10
    MAX_CONCURRENT_REQUESTS: int = 5
    IMAGE_QUALITY_THRESHOLD: int = 6

    # Output settings
    SAVE_IMAGES: bool = False
    SAVE_METADATA: bool = True

    class Config:
        case_sensitive = True
        env_file = ".env"

@lru_cache()
def get_settings() -> Settings:
    """Get application settings with caching."""
    return Settings()

# Create a global settings instance
settings = get_settings()