from typing import Dict, Optional
from datetime import datetime
import asyncio
import uuid
from app.api.websocket import get_websocket_manager

class JobManager:
    def __init__(self):
        self.jobs: Dict[str, dict] = {}
        self.max_concurrent_jobs = 3
        self.active_jobs = 0
        self.job_queue = asyncio.Queue()
        self._running = False

    async def start(self):
        """Start the job manager."""
        self._running = True
        asyncio.create_task(self._process_queue())

    async def stop(self):
        """Stop the job manager."""
        self._running = False
        # Wait for current jobs to complete
        while self.active_jobs > 0:
            await asyncio.sleep(1)

    async def create_job(self, product_names: list, search_front: bool = True, search_back: bool = True) -> str:
        """Create a new job and add it to the queue."""
        job_id = str(uuid.uuid4())
        job_data = {
            "job_id": job_id,
            "status": "queued",
            "created_at": datetime.now(),
            "completed_at": None,
            "total_products": len(product_names),
            "processed_products": 0,
            "successful_products": 0,
            "failed_products": 0,
            "progress": 0.0,
            "product_names": product_names,
            "search_front": search_front,
            "search_back": search_back,
            "error": None
        }

        self.jobs[job_id] = job_data
        await self.job_queue.put(job_id)
        await self._broadcast_update(job_id)
        return job_id

    def get_job_status(self, job_id: str) -> Optional[dict]:
        """Get the current status of a job."""
        return self.jobs.get(job_id)

    async def cancel_job(self, job_id: str) -> bool:
        """Cancel a job if it's still in queue or running."""
        if job_id in self.jobs:
            job = self.jobs[job_id]
            if job["status"] in ["queued", "running"]:
                job["status"] = "cancelled"
                job["error"] = "Job cancelled by user"
                await self._broadcast_update(job_id)
                return True
        return False

    async def _process_queue(self):
        """Process jobs from the queue."""
        while self._running:
            if self.active_jobs < self.max_concurrent_jobs:
                try:
                    job_id = await asyncio.wait_for(self.job_queue.get(), timeout=1.0)
                    asyncio.create_task(self._process_job(job_id))
                except asyncio.TimeoutError:
                    continue
            else:
                await asyncio.sleep(1)

    async def _process_job(self, job_id: str):
        """Process a single job."""
        job = self.jobs[job_id]
        self.active_jobs += 1

        try:
            job["status"] = "running"
            await self._broadcast_update(job_id)

            # Simulate processing for each product
            for i, product in enumerate(job["product_names"]):
                if job["status"] == "cancelled":
                    break

                # TODO: Replace with actual product image automation
                await asyncio.sleep(1)  # Simulate processing time

                job["processed_products"] += 1
                job["successful_products"] += 1
                job["progress"] = (i + 1) / job["total_products"] * 100

                await self._broadcast_update(job_id)

            if job["status"] != "cancelled":
                job["status"] = "completed"
                job["completed_at"] = datetime.now()

        except Exception as e:
            job["status"] = "failed"
            job["error"] = str(e)
        finally:
            self.active_jobs -= 1
            await self._broadcast_update(job_id)

    async def _broadcast_update(self, job_id: str):
        """Broadcast job status update to connected WebSocket clients."""
        websocket_manager = get_websocket_manager()
        job_data = self.get_job_status(job_id)
        if job_data:
            await websocket_manager.broadcast_job_update(job_id, job_data)