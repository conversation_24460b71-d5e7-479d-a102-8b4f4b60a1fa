"""Logging configuration for the product image automation system."""

import logging
import sys
from typing import Any, Dict
from pathlib import Path

try:
    import structlog
    STRUCTLOG_AVAILABLE = True
except ImportError:
    STRUCTLOG_AVAILABLE = False

from .config import settings


def setup_logging() -> logging.Logger:
    """Set up structured logging based on configuration."""

    # Create logs directory
    log_dir = Path(settings.OUTPUT_DIR) / "logs"
    log_dir.mkdir(parents=True, exist_ok=True)

    # Configure basic logging
    logging.basicConfig(
        level=getattr(logging, settings.LOG_LEVEL),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(log_dir / "app.log")
        ]
    )

    if STRUCTLOG_AVAILABLE and settings.LOG_FORMAT == "json":
        # Configure structured logging with structlog
        structlog.configure(
            processors=[
                structlog.stdlib.filter_by_level,
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.stdlib.PositionalArgumentsFormatter(),
                structlog.processors.TimeStamper(fmt="iso"),
                structlog.processors.StackInfoRenderer(),
                structlog.processors.format_exc_info,
                structlog.processors.UnicodeDecoder(),
                structlog.processors.JSONRenderer()
            ],
            context_class=dict,
            logger_factory=structlog.stdlib.LoggerFactory(),
            wrapper_class=structlog.stdlib.BoundLogger,
            cache_logger_on_first_use=True,
        )

        return structlog.get_logger("product_image_automation")
    else:
        # Use standard logging
        return logging.getLogger("product_image_automation")


class LoggerMixin:
    """Mixin class to add logging capabilities to any class."""

    @property
    def logger(self) -> logging.Logger:
        """Get logger instance for the class."""
        if not hasattr(self, '_logger'):
            self._logger = setup_logging()
        return self._logger

    def log_operation(self, operation: str, **kwargs: Any) -> None:
        """Log an operation with structured data."""
        self.logger.info(
            f"Operation: {operation}",
            operation=operation,
            class_name=self.__class__.__name__,
            **kwargs
        )

    def log_error(self, error: Exception, operation: str = None, **kwargs: Any) -> None:
        """Log an error with structured data."""
        self.logger.error(
            f"Error in {operation or 'unknown operation'}: {str(error)}",
            error_type=type(error).__name__,
            error_message=str(error),
            operation=operation,
            class_name=self.__class__.__name__,
            **kwargs
        )

    def log_metrics(self, metrics: Dict[str, Any]) -> None:
        """Log performance metrics."""
        self.logger.info(
            "Performance metrics",
            metrics=metrics,
            class_name=self.__class__.__name__
        )


# Global logger instance
logger = setup_logging()