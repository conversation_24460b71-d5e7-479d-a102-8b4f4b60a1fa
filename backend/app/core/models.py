"""Data models for the product image automation system."""

from datetime import datetime
from enum import Enum
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, HttpUrl, validator


class ViewType(str, Enum):
    """Enumeration for image view types."""
    FRONT = "front"
    BACK = "back"
    SIDE = "side"
    TOP = "top"
    UNKNOWN = "unknown"


class ImageQuality(str, Enum):
    """Enumeration for image quality levels."""
    EXCELLENT = "excellent"  # 9-10
    GOOD = "good"           # 7-8
    FAIR = "fair"           # 5-6
    POOR = "poor"           # 3-4
    VERY_POOR = "very_poor" # 1-2


class ProductComponents(BaseModel):
    """Model for extracted product components."""
    brand: str = Field(..., description="Extracted brand name")
    product_type: str = Field(..., description="Extracted product type/category")
    size_variant: Optional[str] = Field(None, description="Extracted size, flavor, or variant")
    keywords: List[str] = Field(default_factory=list, description="Key identifiers for verification")


class SearchResult(BaseModel):
    """Model for individual search result from Serper.dev."""
    title: str
    image_url: HttpUrl = Field(..., alias="imageUrl")
    source_url: Optional[HttpUrl] = Field(None, alias="link")
    source: Optional[str] = None
    width: Optional[int] = Field(None, alias="imageWidth")
    height: Optional[int] = Field(None, alias="imageHeight")
    thumbnail: Optional[HttpUrl] = Field(None, alias="thumbnailUrl")

    class Config:
        populate_by_name = True


class ImageAssessment(BaseModel):
    """Model for AI assessment of an image."""
    image_url: HttpUrl
    quality_score: int = Field(..., ge=1, le=10)
    product_match: bool
    correct_view: bool
    has_nutrition_info: Optional[bool] = None
    overall_score: int = Field(..., ge=1, le=10)
    reasoning: str
    view_type: ViewType = ViewType.UNKNOWN
    quality_level: Optional[ImageQuality] = None
    processing_time: Optional[float] = None
    error_message: Optional[str] = None
    local_path: Optional[str] = None

    # Enhanced component verification fields
    product_components: Optional[ProductComponents] = None
    product_match_verified: bool = False
    brand_match: bool = False
    product_type_match: bool = False
    size_variant_match: Optional[bool] = None
    mismatch_reason: Optional[str] = None

    # Detailed quality scoring fields
    brand_visibility: float = 0.0
    label_readability: float = 0.0
    product_isolation: float = 0.0
    nutrition_clarity: float = 0.0  # Back view only
    ingredients_visible: float = 0.0  # Back view only
    barcode_visible: float = 0.0  # Back view only
    detected_text: List[str] = Field(default_factory=list)
    quality_issues: List[str] = Field(default_factory=list)

    @validator('quality_level', always=True)
    def set_quality_level(cls, v, values):
        """Automatically set quality level based on overall score."""
        if 'overall_score' in values:
            score = values['overall_score']
            if score >= 9:
                return ImageQuality.EXCELLENT
            elif score >= 7:
                return ImageQuality.GOOD
            elif score >= 5:
                return ImageQuality.FAIR
            elif score >= 3:
                return ImageQuality.POOR
            else:
                return ImageQuality.VERY_POOR
        return v


class ProductSearchRequest(BaseModel):
    """Model for product search request."""
    product_name: str = Field(..., min_length=1, max_length=200)
    search_front_view: bool = True
    search_back_view: bool = True
    max_images_per_view: int = Field(10, ge=1, le=50)
    quality_threshold: int = Field(6, ge=1, le=10)

    @validator('product_name')
    def validate_product_name(cls, v):
        """Validate and clean product name."""
        return v.strip()


class ProductImageResult(BaseModel):
    """Model for the final result of product image processing."""
    product_name: str
    best_front_image: Optional[ImageAssessment] = None
    best_back_image: Optional[ImageAssessment] = None
    all_front_assessments: List[ImageAssessment] = Field(default_factory=list)
    all_back_assessments: List[ImageAssessment] = Field(default_factory=list)
    total_images_processed: int = 0
    processing_time: float = 0.0
    success: bool = True
    error_message: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.now)

    @property
    def has_front_image(self) -> bool:
        """Check if a suitable front image was found."""
        return self.best_front_image is not None

    @property
    def has_back_image(self) -> bool:
        """Check if a suitable back image was found."""
        return self.best_back_image is not None

    @property
    def has_nutrition_info(self) -> bool:
        """Check if back image contains nutrition information."""
        return (
            self.best_back_image is not None and
            self.best_back_image.has_nutrition_info is True
        )

    @property
    def quality_summary(self) -> Dict[str, Any]:
        """Get quality summary statistics."""
        front_score = self.best_front_image.overall_score if self.best_front_image else 0
        back_score = self.best_back_image.overall_score if self.best_back_image else 0

        return {
            "front_image_score": front_score,
            "back_image_score": back_score,
            "average_score": (front_score + back_score) / 2 if (front_score or back_score) else 0,
            "has_nutrition_info": self.has_nutrition_info,
            "total_processed": self.total_images_processed,
            "processing_time": self.processing_time
        }


class BatchProcessingResult(BaseModel):
    """Model for batch processing results."""
    products: List[ProductImageResult]
    total_products: int
    successful_products: int
    failed_products: int
    total_processing_time: float
    average_processing_time: float
    timestamp: datetime = Field(default_factory=datetime.now)

    @validator('successful_products', always=True)
    def count_successful(cls, v, values):
        """Count successful products."""
        if 'products' in values:
            return sum(1 for p in values['products'] if p.success)
        return v

    @validator('failed_products', always=True)
    def count_failed(cls, v, values):
        """Count failed products."""
        if 'products' in values:
            return sum(1 for p in values['products'] if not p.success)
        return v

    @validator('average_processing_time', always=True)
    def calculate_average_time(cls, v, values):
        """Calculate average processing time."""
        if 'products' in values and values['products']:
            total_time = sum(p.processing_time for p in values['products'])
            return total_time / len(values['products'])
        return 0.0

    @property
    def success_rate(self) -> float:
        """Calculate success rate as percentage."""
        if self.total_products == 0:
            return 0.0
        return (self.successful_products / self.total_products) * 100

    @property
    def summary(self) -> Dict[str, Any]:
        """Get processing summary."""
        return {
            "total_products": self.total_products,
            "successful": self.successful_products,
            "failed": self.failed_products,
            "success_rate": f"{self.success_rate:.1f}%",
            "total_time": f"{self.total_processing_time:.2f}s",
            "avg_time_per_product": f"{self.average_processing_time:.2f}s"
        }


class SerperSearchResponse(BaseModel):
    """Model for Serper.dev API response."""
    images: List[SearchResult] = Field(default_factory=list)
    search_metadata: Optional[Dict[str, Any]] = Field(None, alias="searchMetadata")

    class Config:
        allow_population_by_field_name = True