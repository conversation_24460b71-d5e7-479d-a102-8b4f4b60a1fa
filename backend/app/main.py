from fastapi import <PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
from contextlib import asynccontextmanager
from pathlib import Path
import logging

from app.api import products, websocket
from app.core.websocket_manager import WebSocketManager
from app.core.job_manager import <PERSON><PERSON><PERSON>ger

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    logging.info("Starting Product Image Finder Web API")
    job_manager = JobManager()
    await job_manager.start()
    app.state.job_manager = job_manager
    yield
    # Shutdown
    await app.state.job_manager.stop()
    logging.info("Shutting down Product Image Finder Web API")

app = FastAPI(
    title="Product Image Finder",
    description="Web UI for automated product image finding and assessment",
    version="1.0.0",
    lifespan=lifespan
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # TODO: Configure for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files directory for serving images
static_dir = Path("static")
static_dir.mkdir(exist_ok=True)
app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")

# Include routers
app.include_router(products.router)
app.include_router(websocket.router)

# Health check endpoint
@app.get("/health")
async def health_check():
    return JSONResponse({"status": "healthy"})

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)