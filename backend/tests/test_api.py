import pytest
from fastapi.testclient import Test<PERSON>lient
from pathlib import Path
import csv
import io

from app.main import app
from app.core.config import get_settings

settings = get_settings()
client = TestClient(app)

def test_upload_csv_valid():
    """Test valid CSV file upload."""
    content = "product_name\nTest Product 1\nTest Product 2"
    file = io.StringIO(content)
    response = client.post(
        "/api/products/upload/csv",
        files={"file": ("test.csv", file, "text/csv")}
    )
    assert response.status_code == 200
    data = response.json()
    assert "job_id" in data
    assert data["product_count"] == 2

def test_upload_csv_invalid_format():
    """Test invalid file format upload."""
    content = "invalid content"
    file = io.StringIO(content)
    response = client.post(
        "/api/products/upload/csv",
        files={"file": ("test.txt", file, "text/plain")}
    )
    assert response.status_code == 400
    assert "error" in response.json()

def test_get_job_status():
    """Test job status endpoint."""
    # First create a job
    content = "product_name\nTest Product"
    file = io.StringIO(content)
    upload_response = client.post(
        "/api/products/upload/csv",
        files={"file": ("test.csv", file, "text/csv")}
    )
    job_id = upload_response.json()["job_id"]
    
    # Then check its status
    response = client.get(f"/api/jobs/{job_id}")
    assert response.status_code == 200
    data = response.json()
    assert "status" in data
    assert "progress" in data

def test_get_nonexistent_job():
    """Test getting status of non-existent job."""
    response = client.get("/api/jobs/nonexistent-id")
    assert response.status_code == 404

def test_export_results_not_ready():
    """Test exporting results for incomplete job."""
    # Create a new job first
    content = "product_name\nTest Product"
    file = io.StringIO(content)
    upload_response = client.post(
        "/api/products/upload/csv",
        files={"file": ("test.csv", file, "text/csv")}
    )
    job_id = upload_response.json()["job_id"]
    
    # Try to export before completion
    response = client.get(f"/api/jobs/{job_id}/export?format=json")
    assert response.status_code == 400

def test_storage_info():
    """Test storage information endpoint."""
    response = client.get("/storage/info")
    assert response.status_code == 200
    data = response.json()
    assert "total_size_bytes" in data
    assert "total_size_mb" in data

def test_cleanup_storage():
    """Test storage cleanup endpoint."""
    response = client.post("/storage/cleanup")
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)  # List of cleaned up job IDs