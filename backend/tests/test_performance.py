import asyncio
import time
import pytest
from fastapi.testclient import TestClient
import io
from concurrent.futures import ThreadPoolExecutor

from app.main import app

client = TestClient(app)

def upload_csv():
    """Helper function to upload a CSV file."""
    content = "product_name\nTest Product 1\nTest Product 2\nTest Product 3"
    file = io.StringIO(content)
    return client.post(
        "/api/products/upload/csv",
        files={"file": ("test.csv", file, "text/csv")}
    )

def test_concurrent_uploads():
    """Test performance of concurrent file uploads."""
    num_requests = 10
    start_time = time.time()

    with ThreadPoolExecutor(max_workers=5) as executor:
        responses = list(executor.map(lambda _: upload_csv(), range(num_requests)))

    end_time = time.time()
    total_time = end_time - start_time
    avg_response_time = total_time / num_requests

    # Verify responses
    success_count = sum(1 for r in responses if r.status_code == 200)

    print(f"\nConcurrent Upload Performance:")
    print(f"Total Requests: {num_requests}")
    print(f"Successful Requests: {success_count}")
    print(f"Total Time: {total_time:.2f}s")
    print(f"Average Response Time: {avg_response_time:.2f}s")

    assert success_count > 0
    assert avg_response_time < 2.0  # Response time threshold

def test_job_status_latency():
    """Test job status endpoint latency."""
    # First create a job
    response = upload_csv()
    assert response.status_code == 200
    job_id = response.json()["job_id"]

    num_requests = 50
    start_time = time.time()

    for _ in range(num_requests):
        response = client.get(f"/api/jobs/{job_id}")
        assert response.status_code == 200

    end_time = time.time()
    total_time = end_time - start_time
    avg_response_time = total_time / num_requests

    print(f"\nJob Status Endpoint Performance:")
    print(f"Total Requests: {num_requests}")
    print(f"Total Time: {total_time:.2f}s")
    print(f"Average Response Time: {avg_response_time:.2f}s")

    assert avg_response_time < 0.1  # Response time threshold

def test_storage_info_performance():
    """Test storage info endpoint performance."""
    num_requests = 50
    start_time = time.time()

    for _ in range(num_requests):
        response = client.get("/storage/info")
        assert response.status_code == 200

    end_time = time.time()
    total_time = end_time - start_time
    avg_response_time = total_time / num_requests

    print(f"\nStorage Info Endpoint Performance:")
    print(f"Total Requests: {num_requests}")
    print(f"Total Time: {total_time:.2f}s")
    print(f"Average Response Time: {avg_response_time:.2f}s")

    assert avg_response_time < 0.1  # Response time threshold