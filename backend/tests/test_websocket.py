import asyncio
import websockets
import json
import aiohttp

async def test_job_updates():
    # First create a job
    async with aiohttp.ClientSession() as session:
        async with session.post(
            'http://localhost:8000/api/products/process',
            json={
                "product_names": ["Test Product 1", "Test Product 2", "Test Product 3"],
                "search_front": True,
                "search_back": True
            }
        ) as response:
            job_data = await response.json()
            job_id = job_data['job_id']
            print(f"Created job with ID: {job_id}")

    # Connect to WebSocket and listen for updates
    uri = f"ws://localhost:8000/ws/jobs/{job_id}"
    async with websockets.connect(uri) as websocket:
        print(f"Connected to WebSocket for job {job_id}")
        try:
            while True:
                message = await websocket.recv()
                update = json.loads(message)
                print(f"Received update: {json.dumps(update, indent=2)}")

                if update['status'] in ['completed', 'failed', 'cancelled']:
                    break
        except websockets.exceptions.ConnectionClosed:
            print("WebSocket connection closed")

if __name__ == "__main__":
    asyncio.run(test_job_updates())