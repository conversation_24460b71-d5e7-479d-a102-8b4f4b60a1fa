#!/usr/bin/env python3

import asyncio
import websockets
import json

async def test_websocket():
    job_id = "d266221a-7181-4e34-aef8-ea65b595cde1"
    uri = f"ws://localhost:8000/ws/jobs/{job_id}"
    
    try:
        async with websockets.connect(uri) as websocket:
            print(f"Connected to WebSocket for job {job_id}")
            
            # Listen for messages for 30 seconds
            try:
                while True:
                    message = await asyncio.wait_for(websocket.recv(), timeout=30.0)
                    data = json.loads(message)
                    print(f"Received: {json.dumps(data, indent=2)}")
                    
                    if data.get('status') in ['completed', 'failed', 'cancelled']:
                        print("Job finished!")
                        break
                        
            except asyncio.TimeoutError:
                print("No messages received in 30 seconds")
                
    except Exception as e:
        print(f"WebSocket connection failed: {e}")

if __name__ == "__main__":
    asyncio.run(test_websocket())
