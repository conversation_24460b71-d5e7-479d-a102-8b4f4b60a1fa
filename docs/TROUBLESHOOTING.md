# Troubleshooting Guide

## Common Issues and Solutions

### 1. Import Error: "attempted relative import with no known parent package"

**Problem**: This error can occur due to multiple causes related to imports and initialization.

**Causes**:
1. Missing `settings` parameter when initializing `GeminiClient`
2. Relative imports in modules not part of a proper Python package

**Solutions**:
1. For GeminiClient initialization:
```python
from config import settings
from gemini_client import GeminiClient

# Correct way
client = GeminiClient(settings)

# Incorrect way (causes the error)
client = GeminiClient()  # Missing settings parameter
```

2. For relative import issues: Use absolute imports instead of relative imports:
```python
# Instead of: from .models import ProductComponents
from models import ProductComponents
```

### 2. Environment Variable Errors

**Problem**: Missing API keys or incorrect environment variable names.

**Solutions**:

- **GEMINI_API_KEY not set**: Create a `.env` file in the project root with:
  ```
  GEMINI_API_KEY=your_actual_api_key_here
  ```

- **SERPER_API_KEY not set**: Add to your `.env` file:
  ```
  SERPER_API_KEY=your_serper_api_key_here
  ```

### 3. Gemini API Response Issues

**Problem**: <PERSON> returns valid JSON but the application fails to process it.

**Common causes and solutions**:

- **Missing fields in response**: The enhanced prompt should return all required fields. If some are missing, the parsing function provides defaults.

- **Type mismatches**: Ensure numeric fields are properly typed in the JSON response.

- **Large response truncation**: If the response is too long, increase `gemini_max_tokens` in settings.

### 4. Image Download Failures

**Problem**: Images cannot be downloaded or processed.

**Solutions**:

- **Check image URL validity**: Ensure the URL is accessible and points to an actual image.

- **Network timeouts**: Increase `request_timeout` in settings if needed.

- **Image format issues**: Supported formats are JPEG, PNG, WebP, and GIF.

### 5. Testing Issues

**Problem**: Test scripts fail to run or produce unexpected results.

**Solutions**:

- **Use the enhanced test script**: Run `python test_enhanced_assessment.py` which includes a working example.

- **Check API quotas**: Ensure your Gemini API has sufficient quota.

- **Verify image URLs**: Use real, accessible product images for testing.

### 6. Performance Issues

**Problem**: Slow processing or timeouts.

**Solutions**:

- **Reduce concurrent requests**: Lower `max_concurrent_requests` in settings.

- **Optimize image sizes**: Use smaller images when possible.

- **Increase timeouts**: Adjust `request_timeout` for slower networks.

## Getting Help

If you encounter issues not covered here:

1. Check the logs for detailed error messages
2. Verify your environment variables are set correctly
3. Test with the provided example data first
4. Ensure all dependencies are installed: `pip install -r requirements.txt`

## Debug Mode

To enable more detailed logging, set in your `.env` file:
```
LOG_LEVEL=DEBUG
```

This will provide more detailed information about what's happening during processing.