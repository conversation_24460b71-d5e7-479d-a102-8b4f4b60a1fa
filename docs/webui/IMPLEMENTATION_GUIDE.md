# Implementation Guide

## 🎯 Overview

This guide provides detailed technical implementation instructions for building the web UI on top of the existing CLI product image finder. It includes code examples, best practices, and specific guidance for each development phase.

---

## 🏗️ Architecture Overview

### System Components

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React Frontend │    │  FastAPI Backend │    │  Existing CLI   │
│                 │    │                 │    │                 │
│ • Product Input │◄──►│ • REST API      │◄──►│ • Image Search  │
│ • Progress View │    │ • WebSocket     │    │ • AI Assessment │
│ • Image Review  │    │ • Job Manager   │    │ • File Output   │
│ • Results Export│    │ • File Service  │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Technology Stack

**Backend:**
- FastAPI (Python web framework)
- WebSockets (real-time communication)
- Pydantic (data validation)
- aiohttp (async HTTP client)

**Frontend:**
- React 18 with TypeScript
- Vite (build tool)
- Tailwind CSS (styling)
- React Query (data fetching)
- React Hook Form (form handling)

---

## 🔧 Phase 1: Backend Foundation

### Project Structure Setup

```bash
# Create backend directory structure
mkdir -p backend/app/{api,core,models,services,tests}
mkdir -p backend/app/api/{endpoints}
mkdir -p frontend/src/{components,pages,services,hooks,types,utils}
```

### Backend Dependencies

**requirements.txt:**
```txt
# Existing dependencies (from current project)
google-genai>=0.3.0
aiohttp>=3.8.0
requests>=2.28.0
pydantic>=2.0.0
pydantic-settings>=2.0.0
python-dotenv>=1.0.0
Pillow>=9.0.0

# New web framework dependencies
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
websockets>=11.0.0
python-multipart>=0.0.6

# Development dependencies
pytest>=7.4.0
pytest-asyncio>=0.21.0
httpx>=0.25.0
```

### FastAPI Application Setup

**backend/app/main.py:**
```python
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from contextlib import asynccontextmanager
import logging

from app.api.endpoints import products, jobs, files, websocket
from app.core.config import settings
from app.services.job_manager import JobManager
from app.services.websocket_manager import WebSocketManager

# Global managers
job_manager = JobManager()
websocket_manager = WebSocketManager()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    logging.info("Starting Product Image Finder Web API")
    await job_manager.start()
    yield
    # Shutdown
    await job_manager.stop()
    logging.info("Shutting down Product Image Finder Web API")

app = FastAPI(
    title="Product Image Finder API",
    description="Web API for automated product image finding and assessment",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(products.router, prefix="/api/products", tags=["products"])
app.include_router(jobs.router, prefix="/api/jobs", tags=["jobs"])
app.include_router(files.router, prefix="/api/files", tags=["files"])
app.include_router(websocket.router, prefix="/ws", tags=["websocket"])

# Health check
@app.get("/health")
async def health_check():
    return {"status": "healthy", "version": "1.0.0"}

# Serve uploaded files
app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
```

### Configuration Management

**backend/app/core/config.py:**
```python
from pydantic_settings import BaseSettings
from typing import List
import os

class Settings(BaseSettings):
    # API Configuration
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "Product Image Finder"

    # CORS
    ALLOWED_ORIGINS: List[str] = ["http://localhost:3000", "http://localhost:5173"]

    # File Upload
    MAX_FILE_SIZE: int = 50 * 1024 * 1024  # 50MB
    UPLOAD_DIR: str = "uploads"
    ALLOWED_FILE_TYPES: List[str] = [".csv", ".xlsx"]

    # Job Processing
    MAX_CONCURRENT_JOBS: int = 3
    JOB_TIMEOUT: int = 3600  # 1 hour

    # WebSocket
    WEBSOCKET_HEARTBEAT_INTERVAL: int = 30

    # Existing CLI settings (inherit from existing config)
    SERPER_API_KEY: str = ""
    GEMINI_API_KEY: str = ""
    MAX_IMAGES_PER_PRODUCT: int = 10
    MAX_CONCURRENT_REQUESTS: int = 5
    QUALITY_THRESHOLD: float = 0.7

    class Config:
        env_file = ".env"
        case_sensitive = True

settings = Settings()

# Ensure upload directory exists
os.makedirs(settings.UPLOAD_DIR, exist_ok=True)
```

### Automation Wrapper

**backend/app/core/automation_wrapper.py:**
```python
import asyncio
from typing import List, Dict, Any, Callable, Optional
from pathlib import Path
import logging

from product_image_automation import ProductImageAutomation
from models import ProductSearchRequest, BatchProcessingResult
from app.models.job_models import JobProgress, JobStatus

logger = logging.getLogger(__name__)

class AutomationWrapper:
    """Async wrapper for the existing ProductImageAutomation"""

    def __init__(self):
        self.automation = ProductImageAutomation()
        self._progress_callback: Optional[Callable] = None

    def set_progress_callback(self, callback: Callable[[JobProgress], None]):
        """Set callback for progress updates"""
        self._progress_callback = callback

    async def process_products(
        self,
        products: List[str],
        job_id: str,
        output_dir: Optional[str] = None
    ) -> BatchProcessingResult:
        """Process products asynchronously with progress tracking"""

        try:
            # Create progress tracker
            total_products = len(products)
            processed_count = 0

            def progress_callback(product_name: str, status: str, **kwargs):
                nonlocal processed_count
                if status == "completed":
                    processed_count += 1

                progress = JobProgress(
                    job_id=job_id,
                    total_items=total_products,
                    processed_items=processed_count,
                    current_item=product_name,
                    status=status,
                    **kwargs
                )

                if self._progress_callback:
                    self._progress_callback(progress)

            # Convert to ProductSearchRequest objects
            search_requests = [
                ProductSearchRequest(product_name=product)
                for product in products
            ]

            # Run in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                self._run_automation,
                search_requests,
                progress_callback,
                output_dir
            )

            return result

        except Exception as e:
            logger.error(f"Error processing products: {e}")
            raise

    def _run_automation(
        self,
        requests: List[ProductSearchRequest],
        progress_callback: Callable,
        output_dir: Optional[str]
    ) -> BatchProcessingResult:
        """Run automation in synchronous context"""

        # Set up automation with progress callback
        self.automation.set_progress_callback(progress_callback)

        # Set output directory if provided
        if output_dir:
            self.automation.config.output_dir = output_dir

        # Process products
        return self.automation.process_batch(requests)

    async def validate_products(self, products: List[str]) -> Dict[str, Any]:
        """Validate product list"""
        validation_result = {
            "valid_products": [],
            "invalid_products": [],
            "warnings": []
        }

        for product in products:
            if len(product.strip()) == 0:
                validation_result["invalid_products"].append({
                    "product": product,
                    "reason": "Empty product name"
                })
            elif len(product) > 100:
                validation_result["warnings"].append({
                    "product": product,
                    "reason": "Product name is very long"
                })
                validation_result["valid_products"].append(product)
            else:
                validation_result["valid_products"].append(product)

        return validation_result
```

### Job Management

**backend/app/services/job_manager.py:**
```python
import asyncio
import uuid
from typing import Dict, List, Optional, Callable
from datetime import datetime, timedelta
from enum import Enum
import logging

from app.models.job_models import Job, JobStatus, JobProgress
from app.core.automation_wrapper import AutomationWrapper

logger = logging.getLogger(__name__)

class JobManager:
    """Manages background job processing"""

    def __init__(self, max_concurrent_jobs: int = 3):
        self.max_concurrent_jobs = max_concurrent_jobs
        self.jobs: Dict[str, Job] = {}
        self.running_jobs: Dict[str, asyncio.Task] = {}
        self.job_queue: asyncio.Queue = asyncio.Queue()
        self.progress_callbacks: Dict[str, List[Callable]] = {}
        self._worker_task: Optional[asyncio.Task] = None
        self._cleanup_task: Optional[asyncio.Task] = None

    async def start(self):
        """Start the job manager"""
        self._worker_task = asyncio.create_task(self._worker())
        self._cleanup_task = asyncio.create_task(self._cleanup_worker())
        logger.info("Job manager started")

    async def stop(self):
        """Stop the job manager"""
        # Cancel all running jobs
        for task in self.running_jobs.values():
            task.cancel()

        # Cancel worker tasks
        if self._worker_task:
            self._worker_task.cancel()
        if self._cleanup_task:
            self._cleanup_task.cancel()

        logger.info("Job manager stopped")

    async def create_job(
        self,
        products: List[str],
        user_id: Optional[str] = None,
        priority: int = 0
    ) -> str:
        """Create a new processing job"""

        job_id = str(uuid.uuid4())
        job = Job(
            id=job_id,
            products=products,
            user_id=user_id,
            priority=priority,
            status=JobStatus.QUEUED,
            created_at=datetime.utcnow()
        )

        self.jobs[job_id] = job
        await self.job_queue.put(job_id)

        logger.info(f"Created job {job_id} with {len(products)} products")
        return job_id

    async def get_job(self, job_id: str) -> Optional[Job]:
        """Get job by ID"""
        return self.jobs.get(job_id)

    async def get_jobs(self, user_id: Optional[str] = None) -> List[Job]:
        """Get all jobs, optionally filtered by user"""
        jobs = list(self.jobs.values())
        if user_id:
            jobs = [job for job in jobs if job.user_id == user_id]
        return sorted(jobs, key=lambda x: x.created_at, reverse=True)

    async def cancel_job(self, job_id: str) -> bool:
        """Cancel a job"""
        job = self.jobs.get(job_id)
        if not job:
            return False

        if job.status in [JobStatus.QUEUED, JobStatus.RUNNING]:
            job.status = JobStatus.CANCELLED
            job.completed_at = datetime.utcnow()

            # Cancel running task if exists
            if job_id in self.running_jobs:
                self.running_jobs[job_id].cancel()

            logger.info(f"Cancelled job {job_id}")
            return True

        return False

    def add_progress_callback(self, job_id: str, callback: Callable[[JobProgress], None]):
        """Add progress callback for a job"""
        if job_id not in self.progress_callbacks:
            self.progress_callbacks[job_id] = []
        self.progress_callbacks[job_id].append(callback)

    def remove_progress_callback(self, job_id: str, callback: Callable):
        """Remove progress callback for a job"""
        if job_id in self.progress_callbacks:
            try:
                self.progress_callbacks[job_id].remove(callback)
            except ValueError:
                pass

    async def _worker(self):
        """Background worker to process jobs"""
        while True:
            try:
                # Wait for job or timeout
                try:
                    job_id = await asyncio.wait_for(
                        self.job_queue.get(),
                        timeout=1.0
                    )
                except asyncio.TimeoutError:
                    continue

                # Check if we can start more jobs
                if len(self.running_jobs) >= self.max_concurrent_jobs:
                    # Put job back in queue
                    await self.job_queue.put(job_id)
                    await asyncio.sleep(1)
                    continue

                # Start job
                job = self.jobs.get(job_id)
                if job and job.status == JobStatus.QUEUED:
                    task = asyncio.create_task(self._process_job(job_id))
                    self.running_jobs[job_id] = task

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in job worker: {e}")

    async def _process_job(self, job_id: str):
        """Process a single job"""
        job = self.jobs[job_id]

        try:
            # Update job status
            job.status = JobStatus.RUNNING
            job.started_at = datetime.utcnow()

            # Create automation wrapper
            automation = AutomationWrapper()

            # Set up progress callback
            def progress_callback(progress: JobProgress):
                job.progress = progress
                # Notify all callbacks
                for callback in self.progress_callbacks.get(job_id, []):
                    try:
                        callback(progress)
                    except Exception as e:
                        logger.error(f"Error in progress callback: {e}")

            automation.set_progress_callback(progress_callback)

            # Process products
            result = await automation.process_products(
                products=job.products,
                job_id=job_id,
                output_dir=f"uploads/jobs/{job_id}"
            )

            # Update job with results
            job.result = result
            job.status = JobStatus.COMPLETED
            job.completed_at = datetime.utcnow()

            logger.info(f"Completed job {job_id}")

        except asyncio.CancelledError:
            job.status = JobStatus.CANCELLED
            job.completed_at = datetime.utcnow()
            logger.info(f"Job {job_id} was cancelled")

        except Exception as e:
            job.status = JobStatus.FAILED
            job.error_message = str(e)
            job.completed_at = datetime.utcnow()
            logger.error(f"Job {job_id} failed: {e}")

        finally:
            # Remove from running jobs
            self.running_jobs.pop(job_id, None)

    async def _cleanup_worker(self):
        """Clean up old completed jobs"""
        while True:
            try:
                await asyncio.sleep(3600)  # Run every hour

                cutoff_time = datetime.utcnow() - timedelta(days=7)
                jobs_to_remove = []

                for job_id, job in self.jobs.items():
                    if (job.status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED] and
                        job.completed_at and job.completed_at < cutoff_time):
                        jobs_to_remove.append(job_id)

                for job_id in jobs_to_remove:
                    del self.jobs[job_id]
                    self.progress_callbacks.pop(job_id, None)

                if jobs_to_remove:
                    logger.info(f"Cleaned up {len(jobs_to_remove)} old jobs")

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cleanup worker: {e}")
```

### API Models

**backend/app/models/job_models.py:**
```python
from pydantic import BaseModel, Field
from typing import List, Optional, Any, Dict
from datetime import datetime
from enum import Enum

class JobStatus(str, Enum):
    QUEUED = "queued"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class JobProgress(BaseModel):
    job_id: str
    total_items: int
    processed_items: int
    current_item: Optional[str] = None
    status: str
    percentage: float = Field(default=0, ge=0, le=100)
    estimated_time_remaining: Optional[int] = None  # seconds

    def __init__(self, **data):
        super().__init__(**data)
        if self.total_items > 0:
            self.percentage = (self.processed_items / self.total_items) * 100

class Job(BaseModel):
    id: str
    products: List[str]
    user_id: Optional[str] = None
    priority: int = 0
    status: JobStatus = JobStatus.QUEUED
    progress: Optional[JobProgress] = None
    result: Optional[Any] = None
    error_message: Optional[str] = None
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class JobCreateRequest(BaseModel):
    products: List[str] = Field(..., min_items=1, max_items=100)
    priority: int = Field(default=0, ge=0, le=10)

class JobResponse(BaseModel):
    id: str
    status: JobStatus
    progress: Optional[JobProgress] = None
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None

class JobListResponse(BaseModel):
    jobs: List[JobResponse]
    total: int
```

---

## ⚛️ Phase 3: Frontend Foundation

### React Project Setup

```bash
# Create React project with TypeScript
npm create vite@latest frontend -- --template react-ts
cd frontend

# Install dependencies
npm install
npm install -D tailwindcss postcss autoprefixer
npm install react-router-dom react-hook-form @tanstack/react-query
npm install axios lucide-react
npm install @types/node

# Initialize Tailwind CSS
npx tailwindcss init -p
```

### TypeScript Configuration

**frontend/tsconfig.json:**
```json
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  },
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
```

### API Types

**frontend/src/types/api.ts:**
```typescript
// Job Management Types
export enum JobStatus {
  QUEUED = 'queued',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

export interface JobProgress {
  job_id: string;
  total_items: number;
  processed_items: number;
  current_item?: string;
  status: string;
  percentage: number;
  estimated_time_remaining?: number;
}

export interface Job {
  id: string;
  products: string[];
  user_id?: string;
  priority: number;
  status: JobStatus;
  progress?: JobProgress;
  result?: BatchProcessingResult;
  error_message?: string;
  created_at: string;
  started_at?: string;
  completed_at?: string;
}

export interface JobCreateRequest {
  products: string[];
  priority?: number;
}

// Product and Image Types
export interface ImageAssessment {
  image_url: string;
  quality_score: number;
  overall_score: number;
  product_match: boolean;
  correct_view: boolean;
  reasoning: string;
  local_path?: string;
  error_message?: string;
  has_nutrition_info?: boolean;
}

export interface ProductImageResult {
  product_name: string;
  success: boolean;
  processing_time: number;
  front_view_images: ImageAssessment[];
  back_view_images: ImageAssessment[];
  error_message?: string;
}

export interface BatchProcessingResult {
  summary: {
    total_products: number;
    successful_products: number;
    failed_products: number;
    total_processing_time: number;
    average_processing_time: number;
  };
  results: ProductImageResult[];
  metadata?: {
    timestamp: string;
    config_used: Record<string, any>;
  };
}

// WebSocket Message Types
export interface WebSocketMessage {
  type: 'progress' | 'job_status' | 'error' | 'heartbeat';
  data: any;
  timestamp: string;
}

export interface ProgressMessage {
  type: 'progress';
  data: JobProgress;
}

export interface JobStatusMessage {
  type: 'job_status';
  data: {
    job_id: string;
    status: JobStatus;
  };
}

// API Response Types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export interface ApiError {
  detail: string;
  status_code: number;
}

// File Upload Types
export interface FileUploadResponse {
  file_id: string;
  filename: string;
  size: number;
  products: string[];
}

export interface ProductValidationResult {
  valid_products: string[];
  invalid_products: Array<{
    product: string;
    reason: string;
  }>;
  warnings: Array<{
    product: string;
    reason: string;
  }>;
}
```

### API Client Service

**frontend/src/services/api.ts:**
```typescript
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import {
  Job,
  JobCreateRequest,
  ApiResponse,
  FileUploadResponse,
  ProductValidationResult,
  BatchProcessingResult
} from '@/types/api';

class ApiClient {
  private client: AxiosInstance;

  constructor(baseURL: string = 'http://localhost:8000') {
    this.client = axios.create({
      baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response) => {
        console.log(`API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        console.error('API Response Error:', error.response?.data || error.message);
        return Promise.reject(error);
      }
    );
  }

  // Health check
  async healthCheck(): Promise<{ status: string; version: string }> {
    const response = await this.client.get('/health');
    return response.data;
  }

  // Product management
  async validateProducts(products: string[]): Promise<ProductValidationResult> {
    const response = await this.client.post('/api/products/validate', {
      products
    });
    return response.data;
  }

  async uploadProductFile(file: File): Promise<FileUploadResponse> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await this.client.post('/api/products/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  // Job management
  async createJob(request: JobCreateRequest): Promise<{ job_id: string }> {
    const response = await this.client.post('/api/jobs/create', request);
    return response.data;
  }

  async getJob(jobId: string): Promise<Job> {
    const response = await this.client.get(`/api/jobs/${jobId}`);
    return response.data;
  }

  async getJobs(): Promise<Job[]> {
    const response = await this.client.get('/api/jobs');
    return response.data;
  }

  async cancelJob(jobId: string): Promise<{ success: boolean }> {
    const response = await this.client.delete(`/api/jobs/${jobId}`);
    return response.data;
  }

  // File management
  async downloadJobResults(jobId: string, format: 'json' | 'csv' = 'json'): Promise<Blob> {
    const response = await this.client.get(`/api/files/results/${jobId}`, {
      params: { format },
      responseType: 'blob',
    });
    return response.data;
  }

  async getJobImages(jobId: string): Promise<string[]> {
    const response = await this.client.get(`/api/files/images/${jobId}`);
    return response.data;
  }
}

export const apiClient = new ApiClient();
export default ApiClient;
```

### WebSocket Hook

**frontend/src/hooks/useWebSocket.ts:**
```typescript
import { useEffect, useRef, useState, useCallback } from 'react';
import { WebSocketMessage, JobProgress, JobStatus } from '@/types/api';

interface UseWebSocketOptions {
  url: string;
  onMessage?: (message: WebSocketMessage) => void;
  onProgress?: (progress: JobProgress) => void;
  onJobStatusChange?: (jobId: string, status: JobStatus) => void;
  onError?: (error: Event) => void;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
}

interface WebSocketState {
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  reconnectAttempts: number;
}

export const useWebSocket = ({
  url,
  onMessage,
  onProgress,
  onJobStatusChange,
  onError,
  reconnectInterval = 3000,
  maxReconnectAttempts = 5
}: UseWebSocketOptions) => {
  const [state, setState] = useState<WebSocketState>({
    isConnected: false,
    isConnecting: false,
    error: null,
    reconnectAttempts: 0
  });

  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const shouldReconnectRef = useRef(true);

  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    setState(prev => ({ ...prev, isConnecting: true, error: null }));

    try {
      const ws = new WebSocket(url);
      wsRef.current = ws;

      ws.onopen = () => {
        console.log('WebSocket connected');
        setState({
          isConnected: true,
          isConnecting: false,
          error: null,
          reconnectAttempts: 0
        });
      };

      ws.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);

          // Handle different message types
          switch (message.type) {
            case 'progress':
              onProgress?.(message.data);
              break;
            case 'job_status':
              onJobStatusChange?.(message.data.job_id, message.data.status);
              break;
            case 'error':
              console.error('WebSocket error message:', message.data);
              break;
            case 'heartbeat':
              // Handle heartbeat
              break;
          }

          onMessage?.(message);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      ws.onclose = (event) => {
        console.log('WebSocket disconnected:', event.code, event.reason);
        setState(prev => ({ ...prev, isConnected: false, isConnecting: false }));

        // Attempt to reconnect if not manually closed
        if (shouldReconnectRef.current && event.code !== 1000) {
          attemptReconnect();
        }
      };

      ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        setState(prev => ({
          ...prev,
          error: 'Connection error',
          isConnecting: false
        }));
        onError?.(error);
      };

    } catch (error) {
      console.error('Error creating WebSocket:', error);
      setState(prev => ({
        ...prev,
        error: 'Failed to create connection',
        isConnecting: false
      }));
    }
  }, [url, onMessage, onProgress, onJobStatusChange, onError]);

  const attemptReconnect = useCallback(() => {
    if (state.reconnectAttempts >= maxReconnectAttempts) {
      setState(prev => ({
        ...prev,
        error: 'Max reconnection attempts reached'
      }));
      return;
    }

    setState(prev => ({
      ...prev,
      reconnectAttempts: prev.reconnectAttempts + 1
    }));

    reconnectTimeoutRef.current = setTimeout(() => {
      console.log(`Attempting to reconnect... (${state.reconnectAttempts + 1}/${maxReconnectAttempts})`);
      connect();
    }, reconnectInterval);
  }, [state.reconnectAttempts, maxReconnectAttempts, reconnectInterval, connect]);

  const disconnect = useCallback(() => {
    shouldReconnectRef.current = false;

    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }

    if (wsRef.current) {
      wsRef.current.close(1000, 'Manual disconnect');
      wsRef.current = null;
    }

    setState({
      isConnected: false,
      isConnecting: false,
      error: null,
      reconnectAttempts: 0
    });
  }, []);

  const sendMessage = useCallback((message: any) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
      return true;
    }
    return false;
  }, []);

  // Auto-connect on mount
  useEffect(() => {
    shouldReconnectRef.current = true;
    connect();

    return () => {
      disconnect();
    };
  }, [connect, disconnect]);

  return {
    ...state,
    connect,
    disconnect,
    sendMessage
  };
};
```

---

## 🎨 Component Examples

### Product List Manager

**frontend/src/components/ProductListManager.tsx:**
```typescript
import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, X, Plus, AlertCircle } from 'lucide-react';
import { apiClient } from '@/services/api';
import { ProductValidationResult } from '@/types/api';

interface ProductListManagerProps {
  onProductsChange: (products: string[]) => void;
  onValidationChange: (validation: ProductValidationResult | null) => void;
}

export const ProductListManager: React.FC<ProductListManagerProps> = ({
  onProductsChange,
  onValidationChange
}) => {
  const [products, setProducts] = useState<string[]>([]);
  const [newProduct, setNewProduct] = useState('');
  const [isUploading, setIsUploading] = useState(false);
  const [validation, setValidation] = useState<ProductValidationResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (!file) return;

    setIsUploading(true);
    setError(null);

    try {
      const result = await apiClient.uploadProductFile(file);
      setProducts(result.products);
      onProductsChange(result.products);
      await validateProducts(result.products);
    } catch (error) {
      setError('Failed to upload file. Please check the format and try again.');
      console.error('Upload error:', error);
    } finally {
      setIsUploading(false);
    }
  }, [onProductsChange]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/csv': ['.csv'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx']
    },
    multiple: false,
    disabled: isUploading
  });

  const validateProducts = async (productList: string[]) => {
    try {
      const validationResult = await apiClient.validateProducts(productList);
      setValidation(validationResult);
      onValidationChange(validationResult);
    } catch (error) {
      console.error('Validation error:', error);
    }
  };

  const addProduct = () => {
    if (newProduct.trim() && !products.includes(newProduct.trim())) {
      const updatedProducts = [...products, newProduct.trim()];
      setProducts(updatedProducts);
      onProductsChange(updatedProducts);
      setNewProduct('');
      validateProducts(updatedProducts);
    }
  };

  const removeProduct = (index: number) => {
    const updatedProducts = products.filter((_, i) => i !== index);
    setProducts(updatedProducts);
    onProductsChange(updatedProducts);
    validateProducts(updatedProducts);
  };

  return (
    <div className="space-y-6">
      {/* File Upload */}
      <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
        <div {...getRootProps()} className="cursor-pointer">
          <input {...getInputProps()} />
          <div className="text-center">
            <Upload className="mx-auto h-12 w-12 text-gray-400" />
            <div className="mt-4">
              <p className="text-lg font-medium text-gray-900">
                {isDragActive ? 'Drop the file here' : 'Upload product list'}
              </p>
              <p className="text-sm text-gray-500">
                Drag and drop a CSV or Excel file, or click to browse
              </p>
            </div>
          </div>
        </div>
        {isUploading && (
          <div className="mt-4 text-center">
            <div className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-blue-700 bg-blue-100">
              <div className="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-500">
                <div className="h-5 w-5 border-4 border-blue-500 border-t-transparent rounded-full"></div>
              </div>
              Uploading and processing...
            </div>
          </div>
        )}
      </div>

      {/* Manual Entry */}
      <div className="border border-gray-300 rounded-lg p-4">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Add Products Manually</h3>
        <div className="flex space-x-2">
          <input
            type="text"
            value={newProduct}
            onChange={(e) => setNewProduct(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && addProduct()}
            placeholder="Enter product name"
            className="flex-1 border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <button
            onClick={addProduct}
            disabled={!newProduct.trim()}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Plus className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="rounded-md bg-red-50 p-4">
          <div className="flex">
            <AlertCircle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm font-medium text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Product List */}
      {products.length > 0 && (
        <div className="border border-gray-300 rounded-lg p-4">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Products ({products.length})
          </h3>
          <div className="space-y-2 max-h-60 overflow-y-auto">
            {products.map((product, index) => (
              <div key={index} className="flex items-center justify-between bg-gray-50 px-3 py-2 rounded">
                <span className="text-sm text-gray-900">{product}</span>
                <button
                  onClick={() => removeProduct(index)}
                  className="text-red-500 hover:text-red-700"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Validation Results */}
      {validation && (
        <div className="space-y-4">
          {validation.invalid_products.length > 0 && (
            <div className="rounded-md bg-red-50 p-4">
              <h4 className="text-sm font-medium text-red-800 mb-2">
                Invalid Products ({validation.invalid_products.length})
              </h4>
              <ul className="text-sm text-red-700 space-y-1">
                {validation.invalid_products.map((item, index) => (
                  <li key={index}>
                    <strong>{item.product}</strong>: {item.reason}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {validation.warnings.length > 0 && (
            <div className="rounded-md bg-yellow-50 p-4">
              <h4 className="text-sm font-medium text-yellow-800 mb-2">
                Warnings ({validation.warnings.length})
              </h4>
              <ul className="text-sm text-yellow-700 space-y-1">
                {validation.warnings.map((item, index) => (
                  <li key={index}>
                    <strong>{item.product}</strong>: {item.reason}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}
    </div>
  );
};
```

---

## 🚀 Deployment Configuration

### Docker Configuration

**backend/Dockerfile:**
```dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create uploads directory
RUN mkdir -p uploads

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Run application
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

**frontend/Dockerfile:**
```dockerfile
# Build stage
FROM node:18-alpine as build

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci --only=production

# Copy source code
COPY . .

# Build application
RUN npm run build

# Production stage
FROM nginx:alpine

# Copy built application
COPY --from=build /app/dist /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Expose port
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/ || exit 1

CMD ["nginx", "-g", "daemon off;"]
```

**docker-compose.yml:**
```yaml
version: '3.8'

services:
  backend:
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      - SERPER_API_KEY=${SERPER_API_KEY}
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173
    volumes:
      - ./uploads:/app/uploads
      - ./backend:/app
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  frontend:
    build: ./frontend
    ports:
      - "3000:80"
    depends_on:
      - backend
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  uploads:
    driver: local
```

---

## 📝 Development Best Practices

### Code Quality Standards

1. **Python (Backend)**
   - Use Black for code formatting
   - Use flake8 for linting
   - Use mypy for type checking
   - Maintain >80% test coverage

2. **TypeScript (Frontend)**
   - Use Prettier for code formatting
   - Use ESLint for linting
   - Enable strict TypeScript mode
   - Use React Testing Library for testing

3. **Git Workflow**
   - Use conventional commits
   - Create feature branches
   - Require code reviews
   - Run CI/CD checks

### Testing Strategy

1. **Backend Testing**
   ```python
   # Example test file: backend/tests/test_job_manager.py
   import pytest
   from app.services.job_manager import JobManager

   @pytest.mark.asyncio
   async def test_create_job():
       manager = JobManager()
       await manager.start()

       job_id = await manager.create_job(["test product"])
       assert job_id is not None

       job = await manager.get_job(job_id)
       assert job is not None
       assert job.products == ["test product"]

       await manager.stop()
   ```

2. **Frontend Testing**
   ```typescript
   // Example test file: frontend/src/components/__tests__/ProductListManager.test.tsx
   import { render, screen, fireEvent } from '@testing-library/react';
   import { ProductListManager } from '../ProductListManager';

   test('adds product manually', () => {
     const onProductsChange = jest.fn();
     const onValidationChange = jest.fn();

     render(
       <ProductListManager
         onProductsChange={onProductsChange}
         onValidationChange={onValidationChange}
       />
     );

     const input = screen.getByPlaceholderText('Enter product name');
     const addButton = screen.getByRole('button');

     fireEvent.change(input, { target: { value: 'Test Product' } });
     fireEvent.click(addButton);

     expect(onProductsChange).toHaveBeenCalledWith(['Test Product']);
   });
   ```

### Performance Optimization

1. **Backend Optimization**
   - Use async/await for I/O operations
   - Implement connection pooling
   - Add request/response caching
   - Use background tasks for heavy operations

2. **Frontend Optimization**
   - Implement code splitting
   - Use React.memo for expensive components
   - Add virtual scrolling for large lists
   - Optimize bundle size

---

## 🔍 Troubleshooting Guide

### Common Issues

1. **WebSocket Connection Issues**
   ```typescript
   // Add connection retry logic
   const reconnectWebSocket = () => {
     setTimeout(() => {
       if (reconnectAttempts < maxRetries) {
         connect();
         setReconnectAttempts(prev => prev + 1);
       }
     }, reconnectDelay);
   };
   ```

2. **File Upload Errors**
   ```python
   # Add comprehensive file validation
   def validate_upload_file(file: UploadFile):
       if file.size > MAX_FILE_SIZE:
           raise HTTPException(400, "File too large")

       if not file.filename.endswith(('.csv', '.xlsx')):
           raise HTTPException(400, "Invalid file type")
   ```

3. **Memory Issues with Large Datasets**
   ```python
   # Implement streaming for large files
   async def process_large_file(file_path: str):
       async with aiofiles.open(file_path, 'r') as f:
           async for line in f:
               yield process_line(line)
   ```

### Monitoring and Logging

1. **Backend Logging**
   ```python
   import structlog

   logger = structlog.get_logger()

   @app.middleware("http")
   async def log_requests(request: Request, call_next):
       start_time = time.time()
       response = await call_next(request)
       process_time = time.time() - start_time

       logger.info(
           "request_processed",
           method=request.method,
           url=str(request.url),
           status_code=response.status_code,
           process_time=process_time
       )

       return response
   ```

2. **Frontend Error Tracking**
   ```typescript
   // Error boundary for React components
   class ErrorBoundary extends React.Component {
     componentDidCatch(error: Error, errorInfo: ErrorInfo) {
       console.error('React Error:', error, errorInfo);
       // Send to error tracking service
     }
   }
   ```

---

*This implementation guide should be used alongside the PROJECT_ROADMAP.md and DEVELOPMENT_CHECKLIST.md documents. Refer to the existing codebase for integration details and follow the established patterns.*