# Product Image Finder - Technical Specification

## 🔧 API Specification

### Base Configuration
```yaml
API Base URL: http://localhost:8000/api/v1
WebSocket URL: ws://localhost:8000/ws
API Version: v1
Authentication: None (Phase 1), JWT (Future)
```

### REST API Endpoints

#### Product Management
```http
# Upload product list
POST /api/v1/products/upload
Content-Type: multipart/form-data
Body: file (CSV/TXT), config (JSON)
Response: UploadResponse

# Get product list
GET /api/v1/products/{upload_id}
Response: ProductListResponse

# Validate product names
POST /api/v1/products/validate
Body: ProductValidationRequest
Response: ProductValidationResponse
```

#### Processing Jobs
```http
# Start processing job
POST /api/v1/jobs/process
Body: ProcessingJobRequest
Response: JobResponse

# Get job status
GET /api/v1/jobs/{job_id}
Response: JobStatusResponse

# Get job results
GET /api/v1/jobs/{job_id}/results
Response: JobResultsResponse

# Cancel job
DELETE /api/v1/jobs/{job_id}
Response: JobCancelResponse

# List all jobs
GET /api/v1/jobs
Query: status, limit, offset
Response: JobListResponse
```

#### File Management
```http
# Download processed image
GET /api/v1/files/images/{image_id}
Response: Binary image data

# Export results
GET /api/v1/files/export/{job_id}
Query: format (json|csv|html|pdf)
Response: File download

# Get file metadata
GET /api/v1/files/{file_id}/metadata
Response: FileMetadataResponse
```

#### WebSocket Events
```javascript
// Connection
ws://localhost:8000/ws/{job_id}

// Message Types
{
  "type": "job_started",
  "job_id": "uuid",
  "total_products": 10
}

{
  "type": "product_progress",
  "job_id": "uuid",
  "product_name": "Product Name",
  "progress": 0.3,
  "current_step": "searching_images"
}

{
  "type": "product_completed",
  "job_id": "uuid",
  "product_name": "Product Name",
  "result": ProductImageResult
}

{
  "type": "job_completed",
  "job_id": "uuid",
  "summary": BatchProcessingResult
}

{
  "type": "error",
  "job_id": "uuid",
  "error_message": "Error description",
  "error_code": "ERROR_CODE"
}
```

---

## 📊 Data Models

### API Request/Response Models

```python
# Extended from existing models.py
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum

class JobStatus(str, Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class ProcessingStep(str, Enum):
    INITIALIZING = "initializing"
    SEARCHING_IMAGES = "searching_images"
    ASSESSING_IMAGES = "assessing_images"
    DOWNLOADING_IMAGES = "downloading_images"
    FINALIZING = "finalizing"

class UploadResponse(BaseModel):
    upload_id: str
    filename: str
    product_count: int
    validation_errors: List[str] = []
    created_at: datetime

class ProductValidationRequest(BaseModel):
    products: List[str]

class ProductValidationResponse(BaseModel):
    valid_products: List[str]
    invalid_products: List[Dict[str, str]]  # {"product": "reason"}
    suggestions: List[Dict[str, str]] = []  # {"original": "suggested"}

class ProcessingJobRequest(BaseModel):
    upload_id: Optional[str] = None
    products: Optional[List[str]] = None
    config: ProcessingConfig

class ProcessingConfig(BaseModel):
    search_front_view: bool = True
    search_back_view: bool = True
    download_images: bool = True
    max_images_per_view: int = Field(10, ge=1, le=50)
    quality_threshold: int = Field(6, ge=1, le=10)
    max_concurrent: int = Field(3, ge=1, le=10)

class JobResponse(BaseModel):
    job_id: str
    status: JobStatus
    created_at: datetime
    estimated_duration: Optional[int] = None  # seconds

class JobProgress(BaseModel):
    current_product: Optional[str] = None
    current_step: ProcessingStep
    products_completed: int
    total_products: int
    progress_percentage: float
    estimated_time_remaining: Optional[int] = None  # seconds

class JobStatusResponse(BaseModel):
    job_id: str
    status: JobStatus
    progress: Optional[JobProgress] = None
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    config: ProcessingConfig

class JobResultsResponse(BaseModel):
    job_id: str
    results: BatchProcessingResult  # From existing models.py
    download_links: Dict[str, str]  # {"format": "download_url"}

class JobListResponse(BaseModel):
    jobs: List[JobStatusResponse]
    total: int
    limit: int
    offset: int

class FileMetadataResponse(BaseModel):
    file_id: str
    filename: str
    size_bytes: int
    content_type: str
    created_at: datetime
    download_url: str
```

### Frontend TypeScript Types

```typescript
// types/api.ts
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  errors?: string[];
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// types/job.ts
export type JobStatus = 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
export type ProcessingStep = 'initializing' | 'searching_images' | 'assessing_images' | 'downloading_images' | 'finalizing';

export interface Job {
  id: string;
  status: JobStatus;
  progress?: JobProgress;
  createdAt: string;
  startedAt?: string;
  completedAt?: string;
  errorMessage?: string;
  config: ProcessingConfig;
}

export interface JobProgress {
  currentProduct?: string;
  currentStep: ProcessingStep;
  productsCompleted: number;
  totalProducts: number;
  progressPercentage: number;
  estimatedTimeRemaining?: number;
}

// types/product.ts
export interface ProductUpload {
  id: string;
  filename: string;
  productCount: number;
  validationErrors: string[];
  createdAt: string;
}

export interface ProductValidation {
  validProducts: string[];
  invalidProducts: Array<{ product: string; reason: string }>;
  suggestions: Array<{ original: string; suggested: string }>;
}

// types/image.ts
export interface ImageAssessment {
  imageUrl: string;
  qualityScore: number;
  overallScore: number;
  productMatch: boolean;
  correctView: boolean;
  hasNutritionInfo?: boolean;
  reasoning: string;
  viewType: 'front' | 'back' | 'side' | 'top' | 'unknown';
  localPath?: string;
  errorMessage?: string;
}

export interface ProductResult {
  productName: string;
  bestFrontImage?: ImageAssessment;
  bestBackImage?: ImageAssessment;
  allFrontAssessments: ImageAssessment[];
  allBackAssessments: ImageAssessment[];
  totalImagesProcessed: number;
  processingTime: number;
  success: boolean;
  errorMessage?: string;
  timestamp: string;
}
```

---

## 🏗️ Backend Implementation Details

### FastAPI Application Structure

```python
# backend/app/main.py
from fastapi import FastAPI, WebSocket
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from contextlib import asynccontextmanager

from app.api import products, jobs, files, websocket
from app.core.config import settings
from app.core.job_manager import JobManager

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    app.state.job_manager = JobManager()
    yield
    # Shutdown
    await app.state.job_manager.cleanup()

app = FastAPI(
    title="Product Image Finder API",
    description="Web API for automated product image search and processing",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # React dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Static files for images
app.mount("/static", StaticFiles(directory="../output"), name="static")

# API routes
app.include_router(products.router, prefix="/api/v1/products", tags=["products"])
app.include_router(jobs.router, prefix="/api/v1/jobs", tags=["jobs"])
app.include_router(files.router, prefix="/api/v1/files", tags=["files"])
app.include_router(websocket.router, prefix="/ws", tags=["websocket"])

@app.get("/health")
async def health_check():
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}
```

### Job Manager Implementation

```python
# backend/app/core/job_manager.py
import asyncio
import uuid
from typing import Dict, Optional, Callable
from datetime import datetime
from enum import Enum

from app.models.job_models import Job, JobStatus, JobProgress
from app.core.automation_wrapper import AutomationWrapper
from app.core.websocket_manager import WebSocketManager

class JobManager:
    def __init__(self):
        self.jobs: Dict[str, Job] = {}
        self.running_jobs: Dict[str, asyncio.Task] = {}
        self.websocket_manager = WebSocketManager()
        self.max_concurrent_jobs = 3

    async def create_job(
        self,
        products: List[str],
        config: ProcessingConfig
    ) -> str:
        job_id = str(uuid.uuid4())
        job = Job(
            id=job_id,
            status=JobStatus.PENDING,
            products=products,
            config=config,
            created_at=datetime.now()
        )

        self.jobs[job_id] = job

        # Start job if under concurrent limit
        if len(self.running_jobs) < self.max_concurrent_jobs:
            await self._start_job(job_id)

        return job_id

    async def _start_job(self, job_id: str):
        job = self.jobs[job_id]
        job.status = JobStatus.RUNNING
        job.started_at = datetime.now()

        # Create progress callback
        async def progress_callback(progress: JobProgress):
            job.progress = progress
            await self.websocket_manager.broadcast_to_job(
                job_id,
                {
                    "type": "product_progress",
                    "job_id": job_id,
                    "progress": progress.dict()
                }
            )

        # Create automation wrapper
        automation = AutomationWrapper(progress_callback)

        # Start background task
        task = asyncio.create_task(
            self._run_job(job_id, automation)
        )
        self.running_jobs[job_id] = task

    async def _run_job(self, job_id: str, automation: AutomationWrapper):
        try:
            job = self.jobs[job_id]

            # Run the automation
            results = await automation.process_products(
                products=job.products,
                config=job.config
            )

            # Update job with results
            job.status = JobStatus.COMPLETED
            job.completed_at = datetime.now()
            job.results = results

            # Broadcast completion
            await self.websocket_manager.broadcast_to_job(
                job_id,
                {
                    "type": "job_completed",
                    "job_id": job_id,
                    "results": results.dict()
                }
            )

        except Exception as e:
            job.status = JobStatus.FAILED
            job.error_message = str(e)
            job.completed_at = datetime.now()

            await self.websocket_manager.broadcast_to_job(
                job_id,
                {
                    "type": "error",
                    "job_id": job_id,
                    "error_message": str(e)
                }
            )

        finally:
            # Clean up
            if job_id in self.running_jobs:
                del self.running_jobs[job_id]

            # Start next queued job
            await self._start_next_queued_job()

    async def cancel_job(self, job_id: str) -> bool:
        if job_id in self.running_jobs:
            task = self.running_jobs[job_id]
            task.cancel()

            job = self.jobs[job_id]
            job.status = JobStatus.CANCELLED
            job.completed_at = datetime.now()

            return True
        return False

    def get_job(self, job_id: str) -> Optional[Job]:
        return self.jobs.get(job_id)

    def list_jobs(self, limit: int = 50, offset: int = 0) -> List[Job]:
        jobs = list(self.jobs.values())
        jobs.sort(key=lambda x: x.created_at, reverse=True)
        return jobs[offset:offset + limit]
```

### WebSocket Manager

```python
# backend/app/core/websocket_manager.py
import json
from typing import Dict, List, Set
from fastapi import WebSocket

class WebSocketManager:
    def __init__(self):
        # job_id -> list of websocket connections
        self.job_connections: Dict[str, List[WebSocket]] = {}
        # websocket -> job_id mapping
        self.connection_jobs: Dict[WebSocket, str] = {}

    async def connect(self, websocket: WebSocket, job_id: str):
        await websocket.accept()

        if job_id not in self.job_connections:
            self.job_connections[job_id] = []

        self.job_connections[job_id].append(websocket)
        self.connection_jobs[websocket] = job_id

    def disconnect(self, websocket: WebSocket):
        if websocket in self.connection_jobs:
            job_id = self.connection_jobs[websocket]

            if job_id in self.job_connections:
                self.job_connections[job_id].remove(websocket)

                if not self.job_connections[job_id]:
                    del self.job_connections[job_id]

            del self.connection_jobs[websocket]

    async def broadcast_to_job(self, job_id: str, message: dict):
        if job_id in self.job_connections:
            message_str = json.dumps(message)

            # Send to all connections for this job
            disconnected = []
            for websocket in self.job_connections[job_id]:
                try:
                    await websocket.send_text(message_str)
                except:
                    disconnected.append(websocket)

            # Clean up disconnected websockets
            for websocket in disconnected:
                self.disconnect(websocket)
```

---

## 🎨 Frontend Implementation Details

### React Component Architecture

```typescript
// src/components/ProductList/ProductListManager.tsx
import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { ProductUpload, ProductValidation } from '../../types';
import { apiClient } from '../../services/api';

interface ProductListManagerProps {
  onProductsReady: (products: string[], uploadId?: string) => void;
}

export const ProductListManager: React.FC<ProductListManagerProps> = ({
  onProductsReady
}) => {
  const [products, setProducts] = useState<string[]>([]);
  const [validation, setValidation] = useState<ProductValidation | null>(null);
  const [isValidating, setIsValidating] = useState(false);

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (!file) return;

    try {
      const upload = await apiClient.uploadProductList(file);
      setProducts(upload.products);

      // Auto-validate
      setIsValidating(true);
      const validation = await apiClient.validateProducts(upload.products);
      setValidation(validation);

    } catch (error) {
      console.error('Upload failed:', error);
    } finally {
      setIsValidating(false);
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/plain': ['.txt'],
      'text/csv': ['.csv']
    },
    multiple: false
  });

  const handleManualAdd = (productName: string) => {
    setProducts(prev => [...prev, productName]);
  };

  const handleStartProcessing = () => {
    const validProducts = validation?.validProducts || products;
    onProductsReady(validProducts);
  };

  return (
    <div className="space-y-6">
      {/* File Upload Area */}
      <div
        {...getRootProps()}
        className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
          isDragActive ? 'border-blue-400 bg-blue-50' : 'border-gray-300'
        }`}
      >
        <input {...getInputProps()} />
        <div className="space-y-2">
          <div className="text-lg font-medium">
            {isDragActive ? 'Drop the file here' : 'Upload Product List'}
          </div>
          <div className="text-sm text-gray-500">
            Drag & drop a CSV or TXT file, or click to select
          </div>
        </div>
      </div>

      {/* Manual Entry */}
      <ProductManualEntry onAdd={handleManualAdd} />

      {/* Product List */}
      {products.length > 0 && (
        <ProductList
          products={products}
          validation={validation}
          onRemove={(index) => {
            setProducts(prev => prev.filter((_, i) => i !== index));
          }}
        />
      )}

      {/* Validation Results */}
      {validation && (
        <ValidationResults validation={validation} />
      )}

      {/* Start Processing Button */}
      {products.length > 0 && (
        <button
          onClick={handleStartProcessing}
          disabled={isValidating}
          className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 disabled:opacity-50"
        >
          {isValidating ? 'Validating...' : `Start Processing ${products.length} Products`}
        </button>
      )}
    </div>
  );
};
```

### WebSocket Hook

```typescript
// src/hooks/useWebSocket.ts
import { useEffect, useRef, useState } from 'react';
import { JobProgress } from '../types';

interface WebSocketMessage {
  type: string;
  job_id: string;
  [key: string]: any;
}

export const useWebSocket = (jobId: string | null) => {
  const [isConnected, setIsConnected] = useState(false);
  const [progress, setProgress] = useState<JobProgress | null>(null);
  const [error, setError] = useState<string | null>(null);
  const wsRef = useRef<WebSocket | null>(null);

  useEffect(() => {
    if (!jobId) return;

    const ws = new WebSocket(`ws://localhost:8000/ws/${jobId}`);
    wsRef.current = ws;

    ws.onopen = () => {
      setIsConnected(true);
      setError(null);
    };

    ws.onmessage = (event) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data);

        switch (message.type) {
          case 'product_progress':
            setProgress(message.progress);
            break;
          case 'job_completed':
            setProgress(null);
            // Handle completion
            break;
          case 'error':
            setError(message.error_message);
            break;
        }
      } catch (err) {
        console.error('Failed to parse WebSocket message:', err);
      }
    };

    ws.onerror = () => {
      setError('WebSocket connection failed');
    };

    ws.onclose = () => {
      setIsConnected(false);
    };

    return () => {
      ws.close();
    };
  }, [jobId]);

  return { isConnected, progress, error };
};
```

### API Client Service

```typescript
// src/services/api.ts
import axios, { AxiosInstance } from 'axios';
import {
  ProductUpload,
  ProductValidation,
  Job,
  ProcessingConfig,
  ApiResponse
} from '../types';

class ApiClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: 'http://localhost:8000/api/v1',
      timeout: 30000,
    });

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response.data,
      (error) => {
        const message = error.response?.data?.message || error.message;
        throw new Error(message);
      }
    );
  }

  async uploadProductList(file: File): Promise<ProductUpload> {
    const formData = new FormData();
    formData.append('file', file);

    return this.client.post('/products/upload', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
  }

  async validateProducts(products: string[]): Promise<ProductValidation> {
    return this.client.post('/products/validate', { products });
  }

  async startProcessing(
    products: string[],
    config: ProcessingConfig
  ): Promise<Job> {
    return this.client.post('/jobs/process', {
      products,
      config
    });
  }

  async getJob(jobId: string): Promise<Job> {
    return this.client.get(`/jobs/${jobId}`);
  }

  async cancelJob(jobId: string): Promise<void> {
    return this.client.delete(`/jobs/${jobId}`);
  }

  async getJobResults(jobId: string): Promise<any> {
    return this.client.get(`/jobs/${jobId}/results`);
  }

  async exportResults(
    jobId: string,
    format: 'json' | 'csv' | 'html' | 'pdf'
  ): Promise<Blob> {
    const response = await this.client.get(
      `/files/export/${jobId}?format=${format}`,
      { responseType: 'blob' }
    );
    return response.data;
  }
}

export const apiClient = new ApiClient();
```

---

## 🧪 Testing Strategy

### Backend Testing

```python
# tests/test_api.py
import pytest
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

def test_health_check():
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json()["status"] == "healthy"

def test_upload_product_list():
    with open("test_products.txt", "w") as f:
        f.write("Product 1\nProduct 2\nProduct 3")

    with open("test_products.txt", "rb") as f:
        response = client.post(
            "/api/v1/products/upload",
            files={"file": ("test_products.txt", f, "text/plain")}
        )

    assert response.status_code == 200
    data = response.json()
    assert data["product_count"] == 3
    assert len(data["validation_errors"]) == 0

def test_start_processing_job():
    response = client.post(
        "/api/v1/jobs/process",
        json={
            "products": ["Test Product"],
            "config": {
                "search_front_view": True,
                "search_back_view": True,
                "download_images": False,
                "max_images_per_view": 5,
                "quality_threshold": 6,
                "max_concurrent": 1
            }
        }
    )

    assert response.status_code == 200
    data = response.json()
    assert "job_id" in data
    assert data["status"] == "pending"
```

### Frontend Testing

```typescript
// src/components/__tests__/ProductListManager.test.tsx
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ProductListManager } from '../ProductList/ProductListManager';
import { apiClient } from '../../services/api';

// Mock API client
jest.mock('../../services/api');
const mockApiClient = apiClient as jest.Mocked<typeof apiClient>;

describe('ProductListManager', () => {
  const mockOnProductsReady = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders upload area', () => {
    render(<ProductListManager onProductsReady={mockOnProductsReady} />);

    expect(screen.getByText('Upload Product List')).toBeInTheDocument();
    expect(screen.getByText(/Drag & drop a CSV or TXT file/)).toBeInTheDocument();
  });

  test('handles file upload', async () => {
    const mockUpload = {
      upload_id: 'test-id',
      filename: 'test.txt',
      product_count: 2,
      validation_errors: [],
      created_at: new Date().toISOString()
    };

    mockApiClient.uploadProductList.mockResolvedValue(mockUpload);

    render(<ProductListManager onProductsReady={mockOnProductsReady} />);

    const file = new File(['Product 1\nProduct 2'], 'test.txt', {
      type: 'text/plain'
    });

    const input = screen.getByRole('textbox', { hidden: true });
    fireEvent.change(input, { target: { files: [file] } });

    await waitFor(() => {
      expect(mockApiClient.uploadProductList).toHaveBeenCalledWith(file);
    });
  });

  test('starts processing with valid products', async () => {
    render(<ProductListManager onProductsReady={mockOnProductsReady} />);

    // Simulate having products loaded
    // ... test implementation

    const startButton = screen.getByText(/Start Processing/);
    fireEvent.click(startButton);

    expect(mockOnProductsReady).toHaveBeenCalledWith(['Product 1', 'Product 2']);
  });
});
```

---

## 🚀 Deployment Configuration

### Docker Setup

```dockerfile
# backend/Dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create output directories
RUN mkdir -p /app/output/images /app/output/results /app/output/logs

EXPOSE 8000

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

```dockerfile
# frontend/Dockerfile
FROM node:18-alpine as builder

WORKDIR /app

# Install dependencies
COPY package*.json ./
RUN npm ci

# Build application
COPY . .
RUN npm run build

# Production image
FROM nginx:alpine

COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

```yaml
# docker-compose.yml
version: '3.8'

services:
  backend:
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      - SERPER_API_KEY=${SERPER_API_KEY}
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - OUTPUT_DIR=/app/output
    volumes:
      - ./output:/app/output
      - ./backend:/app
    depends_on:
      - redis

  frontend:
    build: ./frontend
    ports:
      - "3000:80"
    depends_on:
      - backend

  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  redis_data:
```

---

This technical specification provides the detailed implementation roadmap for building the web UI. Each section can be implemented incrementally following the phases outlined in the architecture plan.