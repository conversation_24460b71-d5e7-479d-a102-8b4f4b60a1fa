#!/usr/bin/env python3
"""Test script for the enhanced Gemini assessment with product component extraction."""

import asyncio
import os
from dotenv import load_dotenv
from gemini_client import Gemini<PERSON>lient
from models import ViewType
from config import settings

# Load environment variables
load_dotenv()

async def test_enhanced_assessment():
    """Test the enhanced assessment functionality."""

    # Initialize Gemini client
    client = GeminiClient(settings)

    # Test product and image URL (using the same data from the user's log)
    test_product = "LA MOLISANA FUSILLI INTEGRALI NO.28 500G"
    test_image_url = "https://m.media-amazon.com/images/I/7159Q0DLi8L._UF894,1000_QL80_.jpg"

    print(f"Testing enhanced assessment for: {test_product}")
    print(f"Image URL: {test_image_url}")
    print("-" * 50)

    try:
        # Test front view assessment
        print("\n🔍 Testing FRONT view assessment...")
        front_assessment = await client.assess_image(
            image_url=test_image_url,
            product_name=test_product,
            view_type="front"
        )

        print("\n📊 FRONT VIEW RESULTS:")
        print(f"Product Components:")
        if front_assessment.product_components:
            print(f"  - Brand: {front_assessment.product_components.brand}")
            print(f"  - Product Type: {front_assessment.product_components.product_type}")
            print(f"  - Size/Variant: {front_assessment.product_components.size_variant}")
            print(f"  - Keywords: {front_assessment.product_components.keywords}")

        print(f"\nComponent Verification:")
        print(f"  - Product Match Verified: {front_assessment.product_match_verified}")
        print(f"  - Brand Match: {front_assessment.brand_match}")
        print(f"  - Product Type Match: {front_assessment.product_type_match}")
        print(f"  - Size/Variant Match: {front_assessment.size_variant_match}")
        if front_assessment.mismatch_reason:
            print(f"  - Mismatch Reason: {front_assessment.mismatch_reason}")

        print(f"\nQuality Scores:")
        print(f"  - Brand Visibility: {front_assessment.brand_visibility:.1f}/10.0")
        print(f"  - Label Readability: {front_assessment.label_readability:.1f}/10.0")
        print(f"  - Product Isolation: {front_assessment.product_isolation:.1f}/10.0")
        print(f"  - Nutrition Clarity: {front_assessment.nutrition_clarity:.1f}/10.0")
        print(f"  - Ingredients Visible: {front_assessment.ingredients_visible:.1f}/10.0")
        print(f"  - Barcode Visible: {front_assessment.barcode_visible:.1f}/10.0")

        print(f"\nDetected Text: {front_assessment.detected_text}")
        print(f"Quality Issues: {front_assessment.quality_issues}")

        print(f"\nOverall Assessment:")
        print(f"  - Quality Score: {front_assessment.quality_score}/10")
        print(f"  - Overall Score: {front_assessment.overall_score}/10")
        print(f"  - Quality Level: {front_assessment.quality_level}")
        print(f"  - Product Match: {front_assessment.product_match}")
        print(f"  - Correct View: {front_assessment.correct_view}")
        print(f"  - Processing Time: {front_assessment.processing_time:.2f}s")
        print(f"  - Reasoning: {front_assessment.reasoning}")

    except Exception as e:
        print(f"❌ Error during assessment: {str(e)}")
        import traceback
        traceback.print_exc()

    finally:
        # Clean up
        await client.close()

def main():
    """Main function to run the test."""
    print("🚀 Enhanced Gemini Assessment Test")
    print("=" * 50)

    # Check if API key is available
    if not settings.gemini_api_key:
        print("❌ Error: GEMINI_API_KEY environment variable not set")
        print("Please set your Gemini API key in the .env file")
        return

    # Run the test
    asyncio.run(test_enhanced_assessment())
    print("\n✅ Test completed!")

if __name__ == "__main__":
    main()