"""Gemini AI client for image assessment using Google GenAI SDK."""

import asyncio
import io
import json
import time
from typing import Optional, Dict, Any, List
from urllib.parse import urlparse
from io import BytesIO

import aiohttp
from PIL import Image
from google import genai
from google.genai import types

from config import Settings
from logger import LoggerMixin
from models import ImageAssessment, ViewType, ImageQuality


class GeminiClient(LoggerMixin):
    """Client for interacting with Google Gemini AI for image assessment."""

    def __init__(self, settings: Settings, api_key: Optional[str] = None):
        """Initialize Gemini client.

        Args:
            settings: Application settings
            api_key: Gemini API key. If None, uses settings.gemini_api_key
        """
        self.settings = settings
        self.api_key = api_key or settings.gemini_api_key

        # Initialize the Gemini client with API key
        self.client = genai.Client(api_key=self.api_key)

        # Store model configuration
        self.model_name = settings.gemini_model

        # Configure tools with URL context
        self.tools = [
            types.Tool(url_context=types.UrlContext()),
        ]
        self.tools = []  # Empty tools list

        self.generation_config = types.GenerateContentConfig(
            temperature=settings.gemini_temperature,
            max_output_tokens=settings.gemini_max_tokens,
            tools=self.tools
        )

        # HTTP session for downloading images
        self.session: Optional[aiohttp.ClientSession] = None

    async def __aenter__(self):
        """Async context manager entry."""
        await self._ensure_session()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()

    async def _ensure_session(self):
        """Ensure aiohttp session is created."""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=self.settings.request_timeout)
            self.session = aiohttp.ClientSession(
                timeout=timeout,
                headers={'User-Agent': 'ProductImageAutomation/1.0'}
            )

    async def close(self):
        """Close the aiohttp session."""
        if self.session and not self.session.closed:
            await self.session.close()

    def _create_assessment_prompt(
        self,
        product_name: str,
        view_type: str,
        include_nutrition_check: bool = True
    ) -> str:
        """Create an enhanced prompt for image assessment with component extraction.

        Args:
            product_name: Name of the product
            view_type: Expected view type ('front' or 'back')
            include_nutrition_check: Whether to check for nutrition information

        Returns:
            Formatted prompt string
        """
        nutrition_scoring = ""
        if include_nutrition_check and view_type == "back":
            nutrition_scoring = """
    - nutrition_clarity: 0.0-10.0 (clarity of nutrition facts panel)
    - ingredients_visible: 0.0-10.0 (visibility of ingredients list)
    - barcode_visible: 0.0-10.0 (visibility and readability of barcode)"""
        else:
            nutrition_scoring = """
    - nutrition_clarity: 0.0 (set to 0 for front view)
    - ingredients_visible: 0.0 (set to 0 for front view)
    - barcode_visible: 0.0 (set to 0 for front view)"""

        return f"""
Analyze this image for a grocery/retail product called "{product_name}".
This should be a {view_type} view image of the product.

**STEP 1: PRODUCT COMPONENT EXTRACTION**
First, extract the following components from the product name "{product_name}":
- Brand: The manufacturer or brand name
- Product Type: The category/type of product (e.g., "cereal", "shampoo", "chips")
- Size/Variant: Any size, flavor, or variant information (e.g., "500ml", "chocolate", "family size")
- Keywords: Key identifiers for verification

**STEP 2: PRODUCT MATCH VERIFICATION**
Verify if the image shows the correct product by checking:
- Brand Match: Does the visible brand match the extracted brand?
- Product Type Match: Does the product type match?
- Size/Variant Match: If specified, does the size/variant match?

**CRITICAL RULE: If ANY component does not match, set ALL scores to 0 and product_match_verified to false.**

**STEP 3: QUALITY ASSESSMENT** (Only if product matches)
Evaluate these quality criteria (0.0-10.0 scale):
- brand_visibility: How clearly is the brand name visible?
- label_readability: How readable are labels and text?
- product_isolation: How well is the product isolated from background?
{nutrition_scoring}

**STEP 4: OVERALL ASSESSMENT**
- Image Quality (1-10): Resolution, clarity, lighting
- View Correctness: Is this the correct {view_type} view?
- Overall Score (1-10): Combined assessment

Respond ONLY with a valid JSON object in this exact format:
{{
    "product_components": {{
        "brand": "<extracted brand>",
        "product_type": "<extracted product type>",
        "size_variant": "<extracted size/variant or null>",
        "keywords": ["<key1>", "<key2>"]
    }},
    "product_match_verified": <true/false>,
    "brand_match": <true/false>,
    "product_type_match": <true/false>,
    "size_variant_match": <true/false/null>,
    "mismatch_reason": "<reason if no match, null if match>",
    "brand_visibility": <0.0-10.0>,
    "label_readability": <0.0-10.0>,
    "product_isolation": <0.0-10.0>,
    "nutrition_clarity": <0.0-10.0>,
    "ingredients_visible": <0.0-10.0>,
    "barcode_visible": <0.0-10.0>,
    "detected_text": ["<visible text1>", "<visible text2>"],
    "quality_issues": ["<issue1>", "<issue2>"],
    "quality_score": <1-10>,
    "product_match": <true/false>,
    "correct_view": <true/false>,
    "has_nutrition_info": <true/false/null>,
    "overall_score": <1-10>,
    "reasoning": "<brief explanation>"
}}
"""

    def _get_image_mime_type(self, image_data: bytes) -> str:
        """Detect MIME type from image data.

        Args:
            image_data: Raw image bytes

        Returns:
            MIME type string
        """
        try:
            with Image.open(io.BytesIO(image_data)) as img:
                format_lower = img.format.lower() if img.format else 'jpeg'
                if format_lower in ['jpeg', 'jpg']:
                    return 'image/jpeg'
                elif format_lower == 'png':
                    return 'image/png'
                elif format_lower == 'webp':
                    return 'image/webp'
                elif format_lower == 'gif':
                    return 'image/gif'
                else:
                    return 'image/jpeg'  # Default fallback
        except Exception:
            return 'image/jpeg'  # Default fallback

    async def _download_and_validate_image(self, image_url: str) -> Optional[tuple[bytes, str]]:
        """Download and validate an image from URL.

        Args:
            image_url: URL of the image to download

        Returns:
            Tuple of (image_bytes, mime_type) if successful, None if failed
        """
        await self._ensure_session()

        try:
            # Validate URL
            parsed_url = urlparse(image_url)
            if not parsed_url.scheme or not parsed_url.netloc:
                raise ValueError(f"Invalid URL: {image_url}")

            # Download image
            async with self.session.get(image_url) as response:
                if response.status != 200:
                    raise aiohttp.ClientResponseError(
                        request_info=response.request_info,
                        history=response.history,
                        status=response.status
                    )

                # Check content type
                content_type = response.headers.get('content-type', '').lower()
                if not content_type.startswith('image/'):
                    raise ValueError(f"URL does not point to an image: {content_type}")

                # Read image data
                image_data = await response.read()

                # Validate image can be opened and get MIME type
                try:
                    with Image.open(io.BytesIO(image_data)) as img:
                        # Basic validation
                        if img.width < 100 or img.height < 100:
                            raise ValueError("Image too small (minimum 100x100 pixels)")

                        # Check file size (max 10MB)
                        if len(image_data) > 10 * 1024 * 1024:
                            raise ValueError("Image too large (maximum 10MB)")

                except Exception as e:
                    raise ValueError(f"Invalid image format: {str(e)}")

                # Get proper MIME type
                mime_type = self._get_image_mime_type(image_data)
                return image_data, mime_type

        except Exception as e:
            self.log_error(e, "image_download", url=image_url)
            return None

    def _parse_assessment_response(self, response_text: str) -> Dict[str, Any]:
        """Parse Gemini's enhanced JSON response with fallback handling.

        Args:
            response_text: Raw response text from Gemini

        Returns:
            Parsed assessment dictionary
        """
        try:
            # Try to find JSON in the response
            response_text = response_text.strip()

            # Look for JSON object boundaries
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1

            if start_idx == -1 or end_idx == 0:
                raise ValueError("No JSON object found in response")

            json_str = response_text[start_idx:end_idx]
            parsed = json.loads(json_str)

            # Validate required fields
            required_fields = [
                'quality_score', 'product_match', 'correct_view',
                'overall_score', 'reasoning'
            ]

            for field in required_fields:
                if field not in parsed:
                    raise ValueError(f"Missing required field: {field}")

            # Validate score ranges
            for score_field in ['quality_score', 'overall_score']:
                score = parsed[score_field]
                if not isinstance(score, int) or not 1 <= score <= 10:
                    parsed[score_field] = max(1, min(10, int(score) if isinstance(score, (int, float)) else 5))

            # Validate boolean fields
            for bool_field in ['product_match', 'correct_view']:
                if not isinstance(parsed[bool_field], bool):
                    parsed[bool_field] = bool(parsed[bool_field])

            # Handle nutrition info field (can be null)
            if 'has_nutrition_info' in parsed:
                nutrition_val = parsed['has_nutrition_info']
                if nutrition_val is not None and not isinstance(nutrition_val, bool):
                    parsed['has_nutrition_info'] = bool(nutrition_val) if nutrition_val != 'null' else None

            # Validate enhanced fields with defaults
            # Product components
            if 'product_components' not in parsed:
                parsed['product_components'] = {
                    'brand': '',
                    'product_type': '',
                    'size_variant': None,
                    'keywords': []
                }

            # Component verification fields
            parsed['product_match_verified'] = parsed.get('product_match_verified', False)
            parsed['brand_match'] = parsed.get('brand_match', False)
            parsed['product_type_match'] = parsed.get('product_type_match', False)
            parsed['size_variant_match'] = parsed.get('size_variant_match', None)
            parsed['mismatch_reason'] = parsed.get('mismatch_reason', None)

            # Quality scoring fields (0.0-10.0 range)
            quality_fields = [
                'brand_visibility', 'label_readability', 'product_isolation',
                'nutrition_clarity', 'ingredients_visible', 'barcode_visible'
            ]
            for field in quality_fields:
                value = parsed.get(field, 0.0)
                if not isinstance(value, (int, float)) or not 0.0 <= value <= 10.0:
                    parsed[field] = max(0.0, min(10.0, float(value) if isinstance(value, (int, float)) else 0.0))
                else:
                    parsed[field] = float(value)

            # List fields
            parsed['detected_text'] = parsed.get('detected_text', [])
            if not isinstance(parsed['detected_text'], list):
                parsed['detected_text'] = []

            parsed['quality_issues'] = parsed.get('quality_issues', [])
            if not isinstance(parsed['quality_issues'], list):
                parsed['quality_issues'] = []

            return parsed

        except Exception as e:
            self.log_error(e, "response_parsing", response=response_text[:200])

            # Return fallback response
            return {
                "quality_score": 5,
                "product_match": True,
                "correct_view": True,
                "has_nutrition_info": None,
                "overall_score": 5,
                "reasoning": f"Could not parse AI response: {str(e)}"
            }

    async def assess_image(
        self,
        image_url: str,
        product_name: str,
        view_type: str = "front"
    ) -> ImageAssessment:
        """Assess an image using Gemini AI.

        Args:
            image_url: URL of the image to assess
            product_name: Name of the product
            view_type: Expected view type ('front' or 'back')

        Returns:
            ImageAssessment object with AI evaluation results
        """
        start_time = time.time()

        try:
            # Create assessment prompt
            prompt = self._create_assessment_prompt(
                product_name=product_name,
                view_type=view_type,
                include_nutrition_check=(view_type == "back")
            )

            self.log_operation(
                "gemini_assessment_start",
                product=product_name,
                view_type=view_type,
                image_url=str(image_url)[:100] + "..." if len(str(image_url)) > 100 else str(image_url)
            )

            # Download and validate the image first
            image_result = await self._download_and_validate_image(image_url)
            if image_result is None:
                raise ValueError(f"Failed to download or validate image from URL: {image_url}")

            image_data, mime_type = image_result

            # Create content with text and image data using Content structure
            contents = [
                types.Content(
                    role="user",
                    parts=[
                        types.Part.from_text(text=prompt),
                        types.Part.from_bytes(
                            data=image_data,
                            mime_type=mime_type
                        )
                    ],
                ),
            ]

            # Generate response using new API with URL context
            response = await asyncio.to_thread(
                self.client.models.generate_content,
                model=self.model_name,
                contents=contents,
                config=self.generation_config
            )

            # Log the raw JSON response from Gemini
            self.log_operation(
                "gemini_response",
                product=product_name,
                view_type=view_type,
                response_text=response.text
            )
            print(f"Gemini JSON Response: {response.text}")

            # Parse response
            assessment_data = self._parse_assessment_response(response.text)

            # Create ProductComponents object
            from models import ProductComponents
            product_components_data = assessment_data.get('product_components', {})
            product_components = ProductComponents(
                brand=product_components_data.get('brand', ''),
                product_type=product_components_data.get('product_type', ''),
                size_variant=product_components_data.get('size_variant'),
                keywords=product_components_data.get('keywords', [])
            )

            # Create ImageAssessment object with enhanced fields
            assessment = ImageAssessment(
                image_url=image_url,
                quality_score=assessment_data['quality_score'],
                product_match=assessment_data['product_match'],
                correct_view=assessment_data['correct_view'],
                has_nutrition_info=assessment_data.get('has_nutrition_info'),
                overall_score=assessment_data['overall_score'],
                reasoning=assessment_data['reasoning'],
                view_type=ViewType(view_type) if view_type in ViewType.__members__.values() else ViewType.UNKNOWN,
                processing_time=time.time() - start_time,
                # Enhanced component verification fields
                product_components=product_components,
                product_match_verified=assessment_data.get('product_match_verified', False),
                brand_match=assessment_data.get('brand_match', False),
                product_type_match=assessment_data.get('product_type_match', False),
                size_variant_match=assessment_data.get('size_variant_match'),
                mismatch_reason=assessment_data.get('mismatch_reason'),
                # Detailed quality scoring fields
                brand_visibility=assessment_data.get('brand_visibility', 0.0),
                label_readability=assessment_data.get('label_readability', 0.0),
                product_isolation=assessment_data.get('product_isolation', 0.0),
                nutrition_clarity=assessment_data.get('nutrition_clarity', 0.0),
                ingredients_visible=assessment_data.get('ingredients_visible', 0.0),
                barcode_visible=assessment_data.get('barcode_visible', 0.0),
                detected_text=assessment_data.get('detected_text', []),
                quality_issues=assessment_data.get('quality_issues', [])
            )

            self.log_operation(
                "gemini_assessment_success",
                product=product_name,
                view_type=view_type,
                overall_score=assessment.overall_score,
                processing_time=assessment.processing_time
            )

            return assessment

        except Exception as e:
            processing_time = time.time() - start_time

            self.log_error(
                e,
                "gemini_assessment",
                product=product_name,
                view_type=view_type,
                image_url=str(image_url)[:100] + "..." if len(str(image_url)) > 100 else str(image_url),
                processing_time=processing_time
            )

            # Create empty ProductComponents for error case
            from models import ProductComponents
            empty_components = ProductComponents(
                brand="",
                product_type="",
                size_variant=None,
                keywords=[]
            )

            # Return error assessment with all enhanced fields
            return ImageAssessment(
                image_url=image_url,
                quality_score=1,
                product_match=False,
                correct_view=False,
                has_nutrition_info=None,
                overall_score=1,
                reasoning=f"Error during assessment: {str(e)}",
                view_type=ViewType(view_type) if view_type in ViewType.__members__.values() else ViewType.UNKNOWN,
                processing_time=processing_time,
                error_message=str(e),
                # Enhanced component verification fields with default values
                product_components=empty_components,
                product_match_verified=False,
                brand_match=False,
                product_type_match=False,
                size_variant_match=None,
                mismatch_reason=f"Error: {str(e)}",
                # Detailed quality scoring fields with default values
                brand_visibility=0.0,
                label_readability=0.0,
                product_isolation=0.0,
                nutrition_clarity=0.0,
                ingredients_visible=0.0,
                barcode_visible=0.0,
                detected_text=[],
                quality_issues=[f"Error: {str(e)}"]
            )

    async def assess_multiple_images(
        self,
        image_urls: List[str],
        product_name: str,
        view_type: str = "front",
        max_concurrent: int = None
    ) -> List[ImageAssessment]:
        """Assess multiple images concurrently.

        Args:
            image_urls: List of image URLs to assess
            product_name: Name of the product
            view_type: Expected view type
            max_concurrent: Maximum concurrent requests (defaults to settings)

        Returns:
            List of ImageAssessment objects
        """
        if not image_urls:
            return []

        max_concurrent = max_concurrent or self.settings.max_concurrent_requests

        # Create semaphore to limit concurrent requests
        semaphore = asyncio.Semaphore(max_concurrent)

        async def assess_with_semaphore(url: str) -> ImageAssessment:
            async with semaphore:
                return await self.assess_image(url, product_name, view_type)

        # Process all images concurrently
        tasks = [assess_with_semaphore(url) for url in image_urls]
        assessments = await asyncio.gather(*tasks, return_exceptions=True)

        # Handle exceptions
        results = []
        for i, result in enumerate(assessments):
            if isinstance(result, Exception):
                self.log_error(result, "batch_assessment", image_index=i)
                # Create empty ProductComponents for batch error case
                from models import ProductComponents
                empty_components = ProductComponents(
                    brand="",
                    product_type="",
                    size_variant=None,
                    keywords=[]
                )

                # Create error assessment with enhanced fields
                results.append(ImageAssessment(
                    image_url=image_urls[i],
                    quality_score=1,
                    product_match=False,
                    correct_view=False,
                    has_nutrition_info=None,
                    overall_score=1,
                    reasoning=f"Batch processing error: {str(result)}",
                    view_type=ViewType(view_type) if view_type in ViewType.__members__.values() else ViewType.UNKNOWN,
                    error_message=str(result),
                    # Enhanced component verification fields with default values
                    product_components=empty_components,
                    product_match_verified=False,
                    brand_match=False,
                    product_type_match=False,
                    size_variant_match=None,
                    mismatch_reason=f"Batch error: {str(result)}",
                    # Detailed quality scoring fields with default values
                    brand_visibility=0.0,
                    label_readability=0.0,
                    product_isolation=0.0,
                    nutrition_clarity=0.0,
                    ingredients_visible=0.0,
                    barcode_visible=0.0,
                    detected_text=[],
                    quality_issues=[f"Batch error: {str(result)}"]
                ))
            else:
                results.append(result)

        return results

    async def test_connection(self) -> bool:
        """Test connection to Gemini API.

        Returns:
            True if connection is successful, False otherwise
        """
        try:
            # Simple text generation test using Content structure
            test_contents = [
                types.Content(
                    role="user",
                    parts=[
                        types.Part.from_text(text="Hello, this is a test. Please respond with 'OK'.")
                    ],
                ),
            ]
            response = await asyncio.to_thread(
                self.client.models.generate_content,
                model=self.model_name,
                contents=test_contents,
                config=self.generation_config
            )
            return "OK" in response.text or "ok" in response.text.lower()
        except Exception as e:
            self.log_error(e, "connection_test")
            return False