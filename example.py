#!/usr/bin/env python3
"""Example script demonstrating how to use the Product Image Automation system."""

import asyncio
import json
from pathlib import Path

from product_image_automation import ProductImageAutomation
from utils import save_results_to_json, generate_summary_report
from config import settings


async def basic_example():
    """Basic example: Process a single product."""
    print("\n=== Basic Example: Single Product ===")

    async with ProductImageAutomation() as automation:
        result = await automation.process_product(
            product_name="Coca Cola 12oz Can",
            search_front=True,
            search_back=True,
            download_images=True
        )

        print(f"Product: {result.product_name}")
        print(f"Success: {result.success}")
        print(f"Images found: {len(result.images)}")
        print(f"Processing time: {result.processing_time:.2f}s")

        if result.success:
            # Show best images
            front_images = [img for img in result.images if img.view_type == "front"]
            back_images = [img for img in result.images if img.view_type == "back"]

            if front_images:
                best_front = max(front_images, key=lambda x: x.assessment.overall_score)
                print(f"Best front view: {best_front.assessment.overall_score:.1f}/10")

            if back_images:
                best_back = max(back_images, key=lambda x: x.assessment.overall_score)
                print(f"Best back view: {best_back.assessment.overall_score:.1f}/10")
                print(f"Has nutrition info: {best_back.assessment.has_nutrition_info}")
        else:
            print(f"Error: {result.error_message}")


async def batch_example():
    """Batch example: Process multiple products."""
    print("\n=== Batch Example: Multiple Products ===")

    products = [
        "Coca Cola 12oz Can",
        "Pepsi Cola 12oz Can",
        "Sprite 12oz Can",
        "Cheerios Original Cereal"
    ]

    async with ProductImageAutomation() as automation:
        batch_result = await automation.process_product_batch(
            product_names=products,
            search_front=True,
            search_back=True,
            download_images=True,
            max_concurrent=2  # Process 2 products at a time
        )

        print(f"Total products: {batch_result.total_products}")
        print(f"Successful: {batch_result.successful_products}")
        print(f"Failed: {batch_result.failed_products}")
        print(f"Success rate: {batch_result.successful_products/batch_result.total_products:.1%}")
        print(f"Total processing time: {batch_result.total_processing_time:.2f}s")

        # Show results for each product
        for result in batch_result.products:
            status = "✓" if result.success else "✗"
            print(f"  {status} {result.product_name}: {len(result.images)} images")

        # Generate and save summary report
        summary = generate_summary_report(batch_result)

        # Save results to JSON file
        output_path = Path(settings.output_dir) / "results" / "example_results.json"
        save_results_to_json(batch_result, str(output_path))
        print(f"\nResults saved to: {output_path}")

        return batch_result


async def nutrition_focused_example():
    """Example focused on finding nutrition information."""
    print("\n=== Nutrition-Focused Example ===")

    # Focus on food products that should have nutrition labels
    food_products = [
        "Cheerios Original Cereal",
        "Kellogg's Corn Flakes",
        "Quaker Oats Original",
        "Lay's Classic Potato Chips"
    ]

    async with ProductImageAutomation() as automation:
        batch_result = await automation.process_product_batch(
            product_names=food_products,
            search_front=False,  # Only search back views
            search_back=True,
            download_images=True,
            max_concurrent=2
        )

        print(f"Processed {len(food_products)} food products (back views only)")

        nutrition_found = 0
        total_back_images = 0

        for result in batch_result.products:
            if result.success:
                back_images = [img for img in result.images if img.view_type == "back"]
                total_back_images += len(back_images)

                nutrition_images = [
                    img for img in back_images
                    if img.assessment.has_nutrition_info
                ]

                if nutrition_images:
                    nutrition_found += 1
                    best_nutrition = max(nutrition_images, key=lambda x: x.assessment.overall_score)
                    print(f"  ✓ {result.product_name}: Found nutrition info (score: {best_nutrition.assessment.overall_score:.1f}/10)")
                else:
                    print(f"  ✗ {result.product_name}: No nutrition info found")
            else:
                print(f"  ✗ {result.product_name}: Processing failed")

        print(f"\nNutrition info found for {nutrition_found}/{len(food_products)} products")
        print(f"Total back view images analyzed: {total_back_images}")


async def custom_assessment_example():
    """Example showing how to work with detailed assessment data."""
    print("\n=== Custom Assessment Example ===")

    async with ProductImageAutomation() as automation:
        result = await automation.process_product(
            product_name="iPhone 13 Pro",
            search_front=True,
            search_back=True,
            download_images=False  # Just get assessments, don't download
        )

        if result.success:
            print(f"Found {len(result.images)} images for {result.product_name}")

            # Analyze assessment details
            for i, image in enumerate(result.images, 1):
                assessment = image.assessment
                print(f"\nImage {i} ({image.view_type} view):")
                print(f"  URL: {image.url}")
                print(f"  Overall Score: {assessment.overall_score:.1f}/10")
                print(f"  Quality: {assessment.quality.value}")
                print(f"  Relevance Score: {assessment.relevance_score:.1f}/10")
                print(f"  View Correctness: {assessment.view_correctness:.1f}/10")
                print(f"  Has Nutrition Info: {assessment.has_nutrition_info}")

                if assessment.reasoning:
                    print(f"  AI Reasoning: {assessment.reasoning[:100]}...")

            # Find the best overall image
            best_image = max(result.images, key=lambda x: x.assessment.overall_score)
            print(f"\nBest image: {best_image.view_type} view with score {best_image.assessment.overall_score:.1f}/10")
        else:
            print(f"Failed to process {result.product_name}: {result.error_message}")


async def test_system_example():
    """Example showing how to test system connectivity."""
    print("\n=== System Test Example ===")

    async with ProductImageAutomation() as automation:
        test_results = await automation.test_system()

        print("System connectivity test results:")
        for component, passed in test_results.items():
            status = "✓ PASS" if passed else "✗ FAIL"
            print(f"  {component}: {status}")

        all_passed = all(test_results.values())
        print(f"\nOverall system status: {'✓ READY' if all_passed else '✗ ISSUES DETECTED'}")

        return all_passed


async def main():
    """Run all examples."""
    print("Product Image Automation - Example Usage")
    print("=" * 50)

    try:
        # Test system first
        system_ok = await test_system_example()

        if not system_ok:
            print("\n⚠️  System test failed. Please check your configuration.")
            print("Make sure your .env file contains valid API keys.")
            return

        # Run examples
        await basic_example()
        await batch_example()
        await nutrition_focused_example()
        await custom_assessment_example()

        print("\n" + "=" * 50)
        print("✓ All examples completed successfully!")
        print(f"Check the output directory: {settings.output_dir}")

    except KeyboardInterrupt:
        print("\n⚠️  Examples interrupted by user.")
    except Exception as e:
        print(f"\n❌ Error running examples: {e}")
        print("\nTroubleshooting tips:")
        print("1. Check your .env file contains valid API keys")
        print("2. Ensure you have internet connectivity")
        print("3. Verify API quotas/credits are sufficient")
        print("4. Try running with DEBUG logging enabled")


if __name__ == "__main__":
    asyncio.run(main())