# Product Image Automation

An AI-powered system for automating product image search, assessment, and processing for grocery and retail businesses. This tool helps you find high-quality product images (front and back views) with intelligent assessment of image quality, relevance, and nutrition information presence.

## 🎉 Latest Updates (v1.1.0)

- ✅ **Fixed Critical Issues**: Resolved parameter mismatches, attribute access errors, and JSON serialization problems
- 🔧 **Improved Stability**: Enhanced error handling and model consistency across the codebase
- 📊 **Better Reporting**: Fixed output generation for all formats (JSON, CSV, HTML)
- 🚀 **Production Ready**: System now fully operational with comprehensive testing

## Features

- 🔍 **Intelligent Image Search**: Uses Serper.dev API for comprehensive product image search
- 🤖 **AI-Powered Assessment**: Leverages Google Gemini Flash Lite for image quality and relevance evaluation
- 📊 **Dual View Processing**: Searches for both front and back product views
- 🥗 **Nutrition Detection**: Identifies back-view images containing nutrition facts and ingredients
- ⚡ **Concurrent Processing**: Handles multiple products simultaneously for efficiency
- 📈 **Comprehensive Reporting**: Generates JSON, CSV, and HTML reports
- 🎯 **Smart Selection**: Automatically selects the best images based on AI assessment scores
- 🔧 **Configurable**: Extensive configuration options for different use cases

## Installation

### Prerequisites

- Python 3.8 or higher
- API keys for:
  - [Serper.dev](https://serper.dev/) (for image search)
  - [Google AI Studio](https://aistudio.google.com/) (for Gemini API)

### Setup

1. **Clone or download the project files**

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure environment variables**:
   ```bash
   # Copy the example environment file
   cp .env.example .env

   # Edit .env and add your API keys
   nano .env
   ```

4. **Set your API keys in `.env`**:
   ```env
   SERPER_API_KEY=your_serper_api_key_here
   GEMINI_API_KEY=your_gemini_api_key_here
   ```

## Quick Start

### 1. Test System Connectivity

```bash
python cli.py test
```

This will verify that your API keys are working and the system is properly configured.

### 2. Process a Single Product

```bash
python cli.py process "Coca Cola 12oz Can"
```

### 3. Process Multiple Products

```bash
python cli.py process "Coca Cola" "Pepsi" "Sprite" "Cheerios Cereal"
```

### 4. Process Products from a File

Create a text file with product names (one per line):

```bash
# products.txt
Coca Cola 12oz Can
Pepsi Cola 12oz Can
Sprite 12oz Can
Cheerios Original Cereal
Kellogg's Corn Flakes
```

Then process:

```bash
python cli.py process --file products.txt
```

## Usage Examples

### Basic Usage

```bash
# Process single product with default settings
python cli.py process "Apple iPhone 13"

# Process multiple products
python cli.py process "iPhone 13" "Samsung Galaxy S21" "Google Pixel 6"
```

### Advanced Options

```bash
# Search only back views (useful for nutrition info)
python cli.py process --no-front --back "Cheerios Cereal"

# Skip image download (only get URLs and assessments)
python cli.py process --no-download "Coca Cola"

# Increase concurrent processing
python cli.py process --concurrent 5 "Product1" "Product2" "Product3"

# Generate all report formats
python cli.py process --output json,csv,html "Coca Cola"
```

### File Input Formats

The system supports multiple file formats for product lists:

**Text file (products.txt)**:
```
Coca Cola 12oz Can
Pepsi Cola 12oz Can
Sprite 12oz Can
```

**CSV file (products.csv)**:
```csv
product_name
Coca Cola 12oz Can
Pepsi Cola 12oz Can
Sprite 12oz Can
```

**JSON file (products.json)**:
```json
[
  "Coca Cola 12oz Can",
  "Pepsi Cola 12oz Can",
  "Sprite 12oz Can"
]
```

## Configuration

The system can be configured through environment variables in your `.env` file:

### API Configuration
```env
SERPER_API_KEY=your_serper_api_key
GEMINI_API_KEY=your_gemini_api_key
```

### Search Parameters
```env
MAX_IMAGES_PER_SEARCH=10          # Maximum images to fetch per search
MAX_CONCURRENT_REQUESTS=3         # Maximum concurrent API requests
REQUEST_TIMEOUT=30               # Request timeout in seconds
```

### Image Quality
```env
IMAGE_QUALITY_THRESHOLD=6.0      # Minimum quality score (1-10)
MIN_IMAGE_WIDTH=200              # Minimum image width in pixels
MIN_IMAGE_HEIGHT=200             # Minimum image height in pixels
```

### Gemini Model Settings
```env
GEMINI_MODEL=gemini-1.5-flash    # Gemini model to use
GEMINI_TEMPERATURE=0.1           # Model temperature (0.0-1.0)
GEMINI_MAX_TOKENS=1000           # Maximum response tokens
```

### Output Settings
```env
OUTPUT_DIRECTORY=./output        # Directory for downloaded images and reports
LOG_LEVEL=INFO                   # Logging level (DEBUG, INFO, WARNING, ERROR)
USE_STRUCTURED_LOGGING=false     # Enable JSON structured logging
```

## Output

The system generates several types of output:

### 1. Downloaded Images
Images are saved in the output directory with organized folder structure:
```
output/
├── images/
│   ├── coca_cola_12oz_can/
│   │   ├── front_view_1.jpg
│   │   ├── front_view_2.jpg
│   │   ├── back_view_1.jpg
│   │   └── back_view_2.jpg
│   └── pepsi_cola_12oz_can/
│       ├── front_view_1.jpg
│       └── back_view_1.jpg
└── results/
    ├── results_1234567890.json
    ├── results_1234567890.csv
    └── report_1234567890.html
```

### 2. JSON Report
Detailed results in JSON format including:
- Product processing results
- Image URLs and local paths
- AI assessment scores
- Quality metrics
- Processing statistics

### 3. CSV Report
Tabular format suitable for spreadsheet analysis:
- Product name
- Success status
- Number of images found
- Best front/back view scores
- Processing time
- Error messages (if any)

### 4. HTML Report
Visual report with:
- Processing summary
- Image thumbnails
- Quality scores
- Interactive charts
- Detailed product results

## API Usage

You can also use the system programmatically:

```python
import asyncio
from product_image_automation import ProductImageAutomation

async def main():
    async with ProductImageAutomation() as automation:
        # Process a single product
        result = await automation.process_product(
            product_name="Coca Cola 12oz Can",
            search_front=True,
            search_back=True,
            download_images=True
        )

        print(f"Success: {result.success}")
        print(f"Images found: {len(result.images)}")

        # Process multiple products
        batch_result = await automation.process_product_batch(
            product_names=["Coca Cola", "Pepsi", "Sprite"],
            search_front=True,
            search_back=True,
            download_images=True,
            max_concurrent=3
        )

        print(f"Batch success rate: {batch_result.successful_products}/{batch_result.total_products}")

if __name__ == "__main__":
    asyncio.run(main())
```

## Cost Estimation

Based on current API pricing (as of 2024):

### Per 1,000 Products:
- **Serper.dev**: ~$2.50 (assuming 2 searches per product)
- **Google Gemini Flash Lite**: ~$1.00 (for image assessment)
- **Total**: ~$3.50 per 1,000 products

### Factors affecting cost:
- Number of images found per product
- Image assessment complexity
- API rate limits and retries
- Concurrent processing settings

## Troubleshooting

### Recently Fixed Issues ✅

The following issues have been resolved in the latest version:

1. **Parameter Mismatch in Image Search** - Fixed incorrect parameters passed to `search_product_images`
2. **BatchProcessingResult Attribute Access** - Corrected `results.results` to `results.products`
3. **ProductImageResult Attribute References** - Fixed `front_view_images`/`back_view_images` to `all_front_assessments`/`all_back_assessments`
4. **Missing Model Attributes** - Added `local_path` attribute to `ImageAssessment` class
5. **JSON Serialization Issues** - Fixed HttpUrl object serialization in reports

### Common Issues

1. **API Key Errors**
   ```
   Error: Missing required API keys!
   ```
   - Ensure your `.env` file contains valid API keys
   - Check that API keys have sufficient credits/quota

2. **Rate Limit Errors**
   ```
   Rate limit exceeded
   ```
   - Reduce `MAX_CONCURRENT_REQUESTS` in your `.env` file
   - Add delays between requests

3. **Image Download Failures**
   ```
   Failed to download image
   ```
   - Check internet connectivity
   - Some images may have access restrictions
   - Increase `REQUEST_TIMEOUT` for slow connections

4. **Low Quality Results**
   - Adjust `IMAGE_QUALITY_THRESHOLD` in configuration
   - Modify search queries to be more specific
   - Check if products have sufficient online presence

5. **AttributeError Issues**
   ```
   AttributeError: 'BatchProcessingResult' object has no attribute 'results'
   ```
   - This has been fixed in v1.1.0 - ensure you're using the latest version
   - If you encounter this, check that all files are updated consistently

### Debug Mode

Enable debug logging for detailed troubleshooting:

```env
LOG_LEVEL=DEBUG
USE_STRUCTURED_LOGGING=true
```

## Architecture

The system consists of several key components:

- **CLI Interface** (`cli.py`): Command-line interface for easy usage
- **Configuration** (`config.py`): Centralized configuration management
- **Serper Client** (`serper_client.py`): Interface to Serper.dev image search API
- **Gemini Client** (`gemini_client.py`): Interface to Google Gemini AI for image assessment
- **Automation Engine** (`product_image_automation.py`): Main orchestration logic
- **Data Models** (`models.py`): Pydantic models for type safety
- **Utilities** (`utils.py`): Helper functions for reporting and file handling
- **Logging** (`logger.py`): Structured logging configuration

## Code Quality & Best Practices

### Recent Improvements
- ✅ **Type Safety**: Enhanced Pydantic models with proper attribute definitions
- ✅ **Error Handling**: Improved exception handling and graceful degradation
- ✅ **Code Consistency**: Fixed attribute access patterns across all modules
- ✅ **Serialization**: Proper handling of complex objects in JSON/CSV exports

### Recommended Enhancements

For further code quality improvements, consider:

1. **Testing Coverage**
   - Add unit tests for all core functions
   - Integration tests for API interactions
   - Mock external dependencies for reliable testing

2. **Performance Optimization**
   - Implement connection pooling for HTTP requests
   - Add caching for repeated image assessments
   - Optimize concurrent processing limits

3. **Monitoring & Observability**
   - Add metrics collection (processing times, success rates)
   - Implement health checks for external APIs
   - Enhanced structured logging with correlation IDs

4. **Security Enhancements**
   - Input validation for product names
   - Rate limiting implementation
   - Secure handling of API keys

5. **Configuration Management**
   - Environment-specific configuration files
   - Runtime configuration validation
   - Configuration hot-reloading

## Contributing

Contributions are welcome! Please feel free to submit issues, feature requests, or pull requests.

### Development Setup

1. Install development dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Run tests:
   ```bash
   pytest
   ```

3. Format code:
   ```bash
   black .
   flake8 .
   ```

4. Type checking:
   ```bash
   mypy .
   ```

### Code Quality Guidelines

- Follow PEP 8 style guidelines
- Use type hints for all function parameters and return values
- Write comprehensive docstrings for all public methods
- Ensure proper error handling with specific exception types
- Add unit tests for new functionality
- Update documentation for any API changes

## License

This project is provided as-is for educational and commercial use. Please ensure compliance with the terms of service of the APIs used (Serper.dev and Google AI).

## Changelog

### v1.1.0 (Latest)
- **Fixed**: Parameter mismatch in `search_product_images` calls
- **Fixed**: Incorrect `BatchProcessingResult` attribute access (`results` → `products`)
- **Fixed**: Wrong `ProductImageResult` attribute references
- **Added**: Missing `local_path` attribute to `ImageAssessment` model
- **Fixed**: JSON serialization issues with HttpUrl objects
- **Improved**: Error handling and system stability
- **Enhanced**: All output formats (JSON, CSV, HTML) now work correctly

### v1.0.0
- Initial release with core functionality
- AI-powered image search and assessment
- Multi-format reporting
- Concurrent processing support

## Support

For support, please:
1. Check the troubleshooting section above
2. Review the configuration options
3. Enable debug logging to identify issues
4. Ensure you're using the latest version (v1.1.0)
5. Create an issue with detailed error information

---

**Happy automating! 🚀**