"""Main product image automation class that orchestrates the entire workflow."""

import asyncio
import io
import os
import time
from pathlib import Path
from typing import List, Dict, Optional, Tuple
from urllib.parse import urlparse

import aiohttp
from PIL import Image

from config import settings
from logger import LoggerMixin
from models import (
    ProductSearchRequest,
    ProductImageResult,
    BatchProcessingResult,
    SearchResult,
    ImageAssessment,
    ViewType,
    ImageQuality
)
from serper_client import SerperClient
from gemini_client import GeminiClient


class ProductImageAutomation(LoggerMixin):
    """Main class for automating product image search and processing."""

    def __init__(
        self,
        serper_api_key: Optional[str] = None,
        gemini_api_key: Optional[str] = None,
        output_dir: Optional[str] = None
    ):
        """Initialize the automation system.

        Args:
            serper_api_key: Serper.dev API key
            gemini_api_key: Gemini API key
            output_dir: Directory to save processed images
        """
        self.serper_client = SerperClient(api_key=serper_api_key)
        self.gemini_client = GeminiClient(settings, api_key=gemini_api_key)

        # Setup output directory
        self.output_dir = Path(output_dir or settings.output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # Create subdirectories
        (self.output_dir / "images").mkdir(exist_ok=True)
        (self.output_dir / "results").mkdir(exist_ok=True)
        (self.output_dir / "logs").mkdir(exist_ok=True)

        # HTTP session for image downloads
        self.session: Optional[aiohttp.ClientSession] = None

    async def __aenter__(self):
        """Async context manager entry."""
        await self._ensure_session()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()

    async def _ensure_session(self):
        """Ensure aiohttp session is created."""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=settings.request_timeout)
            self.session = aiohttp.ClientSession(
                timeout=timeout,
                headers={'User-Agent': 'ProductImageAutomation/1.0'}
            )

    async def close(self):
        """Close all clients and sessions."""
        if self.session and not self.session.closed:
            await self.session.close()

        await self.serper_client.close()
        await self.gemini_client.close()

    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for safe file system usage.

        Args:
            filename: Original filename

        Returns:
            Sanitized filename
        """
        # Remove or replace invalid characters
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')

        # Limit length
        if len(filename) > 100:
            filename = filename[:100]

        return filename.strip()

    async def _download_image(
        self,
        image_url: str,
        product_name: str,
        view_type: str,
        image_index: int = 0
    ) -> Optional[str]:
        """Download an image and save it locally.

        Args:
            image_url: URL of the image to download
            product_name: Name of the product
            view_type: Type of view (front/back)
            image_index: Index of the image for naming

        Returns:
            Local file path if successful, None if failed
        """
        await self._ensure_session()

        try:
            # Parse URL to get file extension
            parsed_url = urlparse(image_url)
            path_parts = parsed_url.path.split('.')
            extension = path_parts[-1].lower() if len(path_parts) > 1 else 'jpg'

            # Ensure valid image extension
            if extension not in ['jpg', 'jpeg', 'png', 'webp', 'gif']:
                extension = 'jpg'

            # Create filename
            safe_product_name = self._sanitize_filename(product_name)
            filename = f"{safe_product_name}_{view_type}_{image_index}.{extension}"
            file_path = self.output_dir / "images" / filename

            # Download image
            async with self.session.get(image_url) as response:
                if response.status != 200:
                    raise aiohttp.ClientResponseError(
                        request_info=response.request_info,
                        history=response.history,
                        status=response.status
                    )

                # Check content type
                content_type = response.headers.get('content-type', '').lower()
                if not content_type.startswith('image/'):
                    raise ValueError(f"URL does not point to an image: {content_type}")

                # Read and save image
                image_data = await response.read()

                # Validate image
                try:
                    with Image.open(io.BytesIO(image_data)) as img:
                        # Basic validation
                        if img.width < 100 or img.height < 100:
                            raise ValueError("Image too small")

                        # Save image
                        with open(file_path, 'wb') as f:
                            f.write(image_data)

                except Exception as e:
                    raise ValueError(f"Invalid image format: {str(e)}")

                self.log_operation(
                    "image_downloaded",
                    product=product_name,
                    view_type=view_type,
                    file_path=str(file_path),
                    size_bytes=len(image_data)
                )

                return str(file_path)

        except Exception as e:
            self.log_error(
                e,
                "image_download",
                product=product_name,
                view_type=view_type,
                url=image_url
            )
            return None

    def _select_best_images(
        self,
        assessments: List[ImageAssessment],
        max_images: int = 3
    ) -> List[ImageAssessment]:
        """Select the best images based on AI assessment scores.

        Args:
            assessments: List of image assessments
            max_images: Maximum number of images to select

        Returns:
            List of best image assessments
        """
        # Filter out failed assessments and low-quality images
        valid_assessments = [
            assessment for assessment in assessments
            if (
                assessment.product_match and
                assessment.correct_view and
                assessment.overall_score >= settings.image_quality_threshold and
                not assessment.error_message
            )
        ]

        if not valid_assessments:
            # If no valid assessments, return best available
            valid_assessments = sorted(
                assessments,
                key=lambda x: x.overall_score,
                reverse=True
            )[:max_images]

        # Sort by overall score (descending) and select top images
        selected = sorted(
            valid_assessments,
            key=lambda x: (
                x.overall_score,
                x.quality_score,
                1 if x.has_nutrition_info else 0  # Bonus for nutrition info
            ),
            reverse=True
        )[:max_images]

        return selected

    async def process_single_product(
        self,
        product_name: str,
        search_front: bool = True,
        search_back: bool = True,
        download_images: bool = True
    ) -> ProductImageResult:
        """Process a single product to find and assess images.

        Args:
            product_name: Name of the product to search for
            search_front: Whether to search for front view images
            search_back: Whether to search for back view images
            download_images: Whether to download selected images

        Returns:
            ProductImageResult with processing results
        """
        start_time = time.time()

        self.log_operation(
            "product_processing_start",
            product=product_name,
            search_front=search_front,
            search_back=search_back
        )

        try:
            front_results = []
            back_results = []

            # Search for front view images
            if search_front:
                try:
                    search_results = await self.serper_client.search_product_images(
                        product_name=product_name,
                        search_front=True,
                        search_back=False,
                        max_images_per_view=settings.max_images_per_search
                    )
                    front_search_results = search_results.get('front', [])

                    if front_search_results:
                        # Assess front view images
                        front_assessments = await self.gemini_client.assess_multiple_images(
                            image_urls=[str(result.image_url) for result in front_search_results],
                            product_name=product_name,
                            view_type="front"
                        )

                        # Select best front images
                        best_front = self._select_best_images(front_assessments, max_images=3)

                        # Download selected front images
                        if download_images and best_front:
                            for i, assessment in enumerate(best_front):
                                local_path = await self._download_image(
                                    str(assessment.image_url),
                                    product_name,
                                    "front",
                                    i
                                )
                                if local_path:
                                    assessment.local_path = local_path

                        front_results = best_front

                except Exception as e:
                    self.log_error(e, "front_view_processing", product=product_name)

            # Search for back view images
            if search_back:
                try:
                    search_results = await self.serper_client.search_product_images(
                        product_name=product_name,
                        search_front=False,
                        search_back=True,
                        max_images_per_view=settings.max_images_per_search
                    )
                    back_search_results = search_results.get('back', [])

                    if back_search_results:
                        # Assess back view images
                        back_assessments = await self.gemini_client.assess_multiple_images(
                            image_urls=[str(result.image_url) for result in back_search_results],
                            product_name=product_name,
                            view_type="back"
                        )

                        # Select best back images (prioritize nutrition info)
                        best_back = self._select_best_images(back_assessments, max_images=3)

                        # Download selected back images
                        if download_images and best_back:
                            for i, assessment in enumerate(best_back):
                                local_path = await self._download_image(
                                    str(assessment.image_url),
                                    product_name,
                                    "back",
                                    i
                                )
                                if local_path:
                                    assessment.local_path = local_path

                        back_results = best_back

                except Exception as e:
                    self.log_error(e, "back_view_processing", product=product_name)

            # Create result
            processing_time = time.time() - start_time

            result = ProductImageResult(
                product_name=product_name,
                best_front_image=front_results[0] if front_results else None,
                best_back_image=back_results[0] if back_results else None,
                all_front_assessments=front_results,
                all_back_assessments=back_results,
                total_images_processed=len(front_results) + len(back_results),
                processing_time=processing_time,
                success=bool(front_results or back_results)
            )

            self.log_operation(
                "product_processing_complete",
                product=product_name,
                front_images_found=len(front_results),
                back_images_found=len(back_results),
                processing_time=processing_time,
                success=result.success
            )

            return result

        except Exception as e:
            processing_time = time.time() - start_time

            self.log_error(
                e,
                "product_processing",
                product=product_name,
                processing_time=processing_time
            )

            return ProductImageResult(
                product_name=product_name,
                best_front_image=None,
                best_back_image=None,
                all_front_assessments=[],
                all_back_assessments=[],
                total_images_processed=0,
                processing_time=processing_time,
                success=False,
                error_message=str(e)
            )

    async def process_product_batch(
        self,
        product_names: List[str],
        search_front: bool = True,
        search_back: bool = True,
        download_images: bool = True,
        max_concurrent: int = None
    ) -> BatchProcessingResult:
        """Process multiple products concurrently.

        Args:
            product_names: List of product names to process
            search_front: Whether to search for front view images
            search_back: Whether to search for back view images
            download_images: Whether to download selected images
            max_concurrent: Maximum concurrent product processing

        Returns:
            BatchProcessingResult with all processing results
        """
        if not product_names:
            return BatchProcessingResult(
                total_products=0,
                successful_products=0,
                failed_products=0,
                results=[],
                total_processing_time=0.0
            )

        start_time = time.time()
        max_concurrent = max_concurrent or min(settings.max_concurrent_requests, 5)

        self.log_operation(
            "batch_processing_start",
            total_products=len(product_names),
            max_concurrent=max_concurrent
        )

        # Create semaphore to limit concurrent processing
        semaphore = asyncio.Semaphore(max_concurrent)

        async def process_with_semaphore(product_name: str) -> ProductImageResult:
            async with semaphore:
                return await self.process_single_product(
                    product_name=product_name,
                    search_front=search_front,
                    search_back=search_back,
                    download_images=download_images
                )

        # Process all products concurrently
        tasks = [process_with_semaphore(name) for name in product_names]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Handle exceptions and create final results
        final_results = []
        successful_count = 0
        failed_count = 0

        for i, result in enumerate(results):
            if isinstance(result, Exception):
                self.log_error(result, "batch_product_processing", product_index=i)

                # Create error result
                error_result = ProductImageResult(
                    product_name=product_names[i],
                    best_front_image=None,
                    best_back_image=None,
                    all_front_assessments=[],
                    all_back_assessments=[],
                    total_images_processed=0,
                    processing_time=0.0,
                    success=False,
                    error_message=str(result)
                )
                final_results.append(error_result)
                failed_count += 1
            else:
                final_results.append(result)
                if result.success:
                    successful_count += 1
                else:
                    failed_count += 1

        total_processing_time = time.time() - start_time

        batch_result = BatchProcessingResult(
            products=final_results,
            total_products=len(product_names),
            successful_products=successful_count,
            failed_products=failed_count,
            total_processing_time=total_processing_time,
            average_processing_time=total_processing_time / len(product_names) if product_names else 0.0
        )

        self.log_operation(
            "batch_processing_complete",
            total_products=batch_result.total_products,
            successful_products=batch_result.successful_products,
            failed_products=batch_result.failed_products,
            total_processing_time=batch_result.total_processing_time
        )

        return batch_result

    async def test_system(self) -> Dict[str, bool]:
        """Test all system components.

        Returns:
            Dictionary with test results for each component
        """
        results = {}

        # Test Serper client
        try:
            results['serper'] = await self.serper_client.test_connection()
        except Exception as e:
            self.log_error(e, "serper_test")
            results['serper'] = False

        # Test Gemini client
        try:
            results['gemini'] = await self.gemini_client.test_connection()
        except Exception as e:
            self.log_error(e, "gemini_test")
            results['gemini'] = False

        # Test output directory
        try:
            test_file = self.output_dir / "test.txt"
            test_file.write_text("test")
            test_file.unlink()
            results['output_directory'] = True
        except Exception as e:
            self.log_error(e, "output_directory_test")
            results['output_directory'] = False

        return results